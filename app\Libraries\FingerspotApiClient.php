<?php

namespace App\Libraries;

use CodeIgniter\HTTP\CURLRequest;
use Config\Fingerspot;
use Config\Services;
use Exception;

/**
 * Fingerspot API Client
 * 
 * Main client for interacting with Fingerspot.io Developer API
 */
class FingerspotApiClient
{
    protected CURLRequest $client;
    protected Fingerspot $config;
    protected array $defaultOptions;

    public function __construct()
    {
        $this->config = config('Fingerspot');
        $this->client = Services::curlrequest();
        
        $this->defaultOptions = [
            'timeout' => $this->config->timeout,
            'headers' => array_merge($this->config->defaultHeaders, [
                'Authorization' => 'Bearer ' . $this->config->apiToken,
                'X-Cloud-ID' => $this->config->cloudId,
            ]),
            'verify' => false, // For development, set to true in production
        ];
    }

    /**
     * Make GET request to API
     */
    public function get(string $endpoint, array $params = []): array
    {
        $url = $this->buildUrl($endpoint);
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        try {
            $response = $this->client->get($url, $this->defaultOptions);
            return $this->handleResponse($response);
        } catch (Exception $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Make POST request to API
     */
    public function post(string $endpoint, array $data = []): array
    {
        $url = $this->buildUrl($endpoint);
        
        $options = array_merge($this->defaultOptions, [
            'json' => $data
        ]);

        try {
            $response = $this->client->post($url, $options);
            return $this->handleResponse($response);
        } catch (Exception $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Make PUT request to API
     */
    public function put(string $endpoint, array $data = []): array
    {
        $url = $this->buildUrl($endpoint);
        
        $options = array_merge($this->defaultOptions, [
            'json' => $data
        ]);

        try {
            $response = $this->client->put($url, $options);
            return $this->handleResponse($response);
        } catch (Exception $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Make DELETE request to API
     */
    public function delete(string $endpoint, array $params = []): array
    {
        $url = $this->buildUrl($endpoint);
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        try {
            $response = $this->client->delete($url, $this->defaultOptions);
            return $this->handleResponse($response);
        } catch (Exception $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Build full URL for endpoint
     */
    protected function buildUrl(string $endpoint): string
    {
        return rtrim($this->config->baseUrl, '/') . '/' . ltrim($endpoint, '/');
    }

    /**
     * Handle API response
     */
    protected function handleResponse($response): array
    {
        $statusCode = $response->getStatusCode();
        $body = $response->getBody();
        
        if ($this->config->debug) {
            log_message('debug', 'Fingerspot API Response: ' . $body);
        }

        $data = json_decode($body, true);
        
        if ($statusCode >= 200 && $statusCode < 300) {
            return [
                'success' => true,
                'status_code' => $statusCode,
                'data' => $data,
                'message' => 'Request successful'
            ];
        }

        return [
            'success' => false,
            'status_code' => $statusCode,
            'data' => $data,
            'message' => $data['message'] ?? 'API request failed'
        ];
    }

    /**
     * Handle API errors
     */
    protected function handleError(Exception $e): array
    {
        if ($this->config->debug) {
            log_message('error', 'Fingerspot API Error: ' . $e->getMessage());
        }

        return [
            'success' => false,
            'status_code' => 0,
            'data' => null,
            'message' => $e->getMessage()
        ];
    }

    /**
     * Get endpoint URL from config
     */
    public function getEndpoint(string $key): string
    {
        return $this->config->endpoints[$key] ?? $key;
    }
}
