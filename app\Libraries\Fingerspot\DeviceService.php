<?php

namespace App\Libraries\Fingerspot;

use App\Libraries\FingerspotApiClient;

/**
 * Fingerspot Device Service
 * 
 * Service for handling device management operations
 */
class DeviceService
{
    protected FingerspotApiClient $client;

    public function __construct()
    {
        $this->client = new FingerspotApiClient();
    }

    /**
     * Get device information
     * 
     * @return array
     */
    public function getDeviceInfo(): array
    {
        $endpoint = $this->client->getEndpoint('device_info');
        return $this->client->get($endpoint);
    }

    /**
     * Restart device
     * 
     * @return array
     */
    public function restartDevice(): array
    {
        $endpoint = $this->client->getEndpoint('restart_device');
        return $this->client->post($endpoint);
    }

    /**
     * Set device time
     * 
     * @param string|null $datetime Datetime string (Y-m-d H:i:s), null for current time
     * @param string|null $timezone Timezone string, null for default
     * @return array
     */
    public function setTime(?string $datetime = null, ?string $timezone = null): array
    {
        $data = [];
        
        if ($datetime) {
            $data['datetime'] = $datetime;
        } else {
            $data['datetime'] = date('Y-m-d H:i:s');
        }

        if ($timezone) {
            $data['timezone'] = $timezone;
        }

        $endpoint = $this->client->getEndpoint('set_time');
        return $this->client->post($endpoint, $data);
    }

    /**
     * Set device timezone
     * 
     * @param string $timezone Timezone string (e.g., 'Asia/Jakarta')
     * @return array
     */
    public function setTimezone(string $timezone): array
    {
        return $this->setTime(null, $timezone);
    }

    /**
     * Sync device time with server
     * 
     * @return array
     */
    public function syncTime(): array
    {
        return $this->setTime();
    }

    /**
     * Get device status
     * 
     * @return array
     */
    public function getStatus(): array
    {
        $endpoint = $this->client->getEndpoint('device_info') . '/status';
        return $this->client->get($endpoint);
    }

    /**
     * Get device capacity information
     * 
     * @return array
     */
    public function getCapacity(): array
    {
        $endpoint = $this->client->getEndpoint('device_info') . '/capacity';
        return $this->client->get($endpoint);
    }

    /**
     * Get device firmware version
     * 
     * @return array
     */
    public function getFirmwareVersion(): array
    {
        $endpoint = $this->client->getEndpoint('device_info') . '/firmware';
        return $this->client->get($endpoint);
    }

    /**
     * Clear all data from device
     * 
     * @param string $dataType Type of data to clear ('all', 'users', 'logs', 'templates')
     * @return array
     */
    public function clearData(string $dataType = 'all'): array
    {
        $data = ['data_type' => $dataType];
        $endpoint = $this->client->getEndpoint('device_info') . '/clear';
        return $this->client->post($endpoint, $data);
    }

    /**
     * Enable or disable device
     * 
     * @param bool $enable True to enable, false to disable
     * @return array
     */
    public function setDeviceEnabled(bool $enable): array
    {
        $data = ['enabled' => $enable];
        $endpoint = $this->client->getEndpoint('device_info') . '/enable';
        return $this->client->post($endpoint, $data);
    }

    /**
     * Set device configuration
     * 
     * @param array $config Configuration parameters
     * @return array
     */
    public function setConfiguration(array $config): array
    {
        $endpoint = $this->client->getEndpoint('device_info') . '/config';
        return $this->client->post($endpoint, $config);
    }

    /**
     * Get device configuration
     * 
     * @return array
     */
    public function getConfiguration(): array
    {
        $endpoint = $this->client->getEndpoint('device_info') . '/config';
        return $this->client->get($endpoint);
    }

    /**
     * Test device connection
     * 
     * @return array
     */
    public function testConnection(): array
    {
        $endpoint = $this->client->getEndpoint('device_info') . '/ping';
        return $this->client->get($endpoint);
    }

    /**
     * Get device network information
     * 
     * @return array
     */
    public function getNetworkInfo(): array
    {
        $endpoint = $this->client->getEndpoint('device_info') . '/network';
        return $this->client->get($endpoint);
    }
}
