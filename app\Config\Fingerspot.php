<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

/**
 * Fingerspot API Configuration
 * 
 * Configuration for Fingerspot.io Developer API
 */
class Fingerspot extends BaseConfig
{
    /**
     * Fingerspot API Base URL
     */
    public string $baseUrl = 'https://developer.fingerspot.io/api';

    /**
     * API Token for authentication
     */
    public string $apiToken = '4FWPFC5UR2M4Y7G6';

    /**
     * Cloud ID for device identification
     */
    public string $cloudId = 'C2630450C3233D26';

    /**
     * Request timeout in seconds
     */
    public int $timeout = 30;

    /**
     * Enable debug mode for logging
     */
    public bool $debug = false;

    /**
     * Default headers for API requests
     */
    public array $defaultHeaders = [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
    ];

    /**
     * API endpoints configuration
     */
    public array $endpoints = [
        'device_info' => '/device/info',
        'attendance_logs' => '/attendance/logs',
        'user_info' => '/user/info',
        'set_user' => '/user/set',
        'delete_user' => '/user/delete',
        'set_time' => '/device/time',
        'restart_device' => '/device/restart',
        'register_online' => '/user/register',
    ];
}
