<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

/**
 * Fingerspot API Configuration
 * 
 * Configuration for Fingerspot.io Developer API
 */
class Fingerspot extends BaseConfig
{
    /**
     * Fingerspot API Base URL
     */
    public string $baseUrl;

    /**
     * API Token for authentication
     */
    public string $apiToken;

    /**
     * Cloud ID for device identification
     */
    public string $cloudId;

    public function __construct()
    {
        parent::__construct();

        // Load from environment variables with fallback to default values
        $this->baseUrl = env('fingerspot.baseUrl', 'https://developer.fingerspot.io/api');
        $this->apiToken = env('fingerspot.apiToken', '4FWPFC5UR2M4Y7G6');
        $this->cloudId = env('fingerspot.cloudId', 'C2630450C3233D26');
    }

    /**
     * Request timeout in seconds
     */
    public int $timeout = 30;

    /**
     * Enable debug mode for logging
     */
    public bool $debug = false;

    /**
     * Default headers for API requests
     */
    public array $defaultHeaders = [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
    ];

    /**
     * API endpoints configuration (Updated to match working cURL implementation)
     */
    public array $endpoints = [
        'device_info' => '/get_device_info',
        'attendance_logs' => '/get_attlog',
        'user_info' => '/get_userinfo',
        'set_user' => '/set_userinfo',
        'delete_user' => '/delete_user',
        'set_time' => '/set_time',
        'restart_device' => '/restart_device',
        'register_online' => '/register_online',
    ];
}
