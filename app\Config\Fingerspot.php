<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

/**
 * Fingerspot API Configuration
 * 
 * Configuration for Fingerspot.io Developer API
 */
class Fingerspot extends BaseConfig
{
    /**
     * Fingerspot API Base URL
     */
    public string $baseUrl;

    /**
     * API Token for authentication
     */
    public string $apiToken;

    /**
     * Cloud ID for device identification
     */
    public string $cloudId;

    public function __construct()
    {
        parent::__construct();

        // Load from environment variables with fallback to default values
        $this->baseUrl = env('fingerspot.baseUrl', 'https://developer.fingerspot.io/api');
        $this->apiToken = env('fingerspot.apiToken', '4FWPFC5UR2M4Y7G6');
        $this->cloudId = env('fingerspot.cloudId', 'C2630450C3233D26');
    }

    /**
     * Request timeout in seconds
     */
    public int $timeout = 30;

    /**
     * Enable debug mode for logging
     */
    public bool $debug = false;

    /**
     * Default headers for API requests
     */
    public array $defaultHeaders = [
        'Content-Type' => 'application/json',
        'Accept' => 'application/json',
    ];

    /**
     * API endpoints configuration
     */
    public array $endpoints = [
        'device_info' => '/device/info',
        'attendance_logs' => '/attendance/logs',
        'user_info' => '/user/info',
        'set_user' => '/user/set',
        'delete_user' => '/user/delete',
        'set_time' => '/device/time',
        'restart_device' => '/device/restart',
        'register_online' => '/user/register',
    ];
}
