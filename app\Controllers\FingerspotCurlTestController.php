<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\FingerspotCurlClient;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Fingerspot cURL Test Controller
 * 
 * Tests the exact cURL implementation from PDF documentation
 * using environment variables for API token and cloud_id
 */
class FingerspotCurlTestController extends BaseController
{
    protected FingerspotCurlClient $curlClient;

    public function __construct()
    {
        $this->curlClient = new FingerspotCurlClient();
    }

    /**
     * Test the exact get_attlog implementation from PDF
     */
    public function getAttlog(): ResponseInterface
    {
        // Get parameters from request
        $transId = $this->request->getGet('trans_id') ?? '1';
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-2 days'));
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');

        // Call the exact cURL implementation
        $result = $this->curlClient->getAttlog($transId, $startDate, $endDate);

        return $this->response->setJSON([
            'pdf_curl_example' => 'get_attlog',
            'description' => 'Exact cURL implementation from PDF documentation',
            'parameters' => [
                'trans_id' => $transId,
                'cloud_id' => env('fingerspot.cloudId'),
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'curl_code_used' => [
                'url' => 'https://developer.fingerspot.io/api/get_attlog',
                'method' => 'POST',
                'headers' => [
                    'Content-Type: application/json',
                    'Authorization: Bearer [api_token from env]'
                ],
                'ssl_verify' => false,
                'follow_location' => true
            ],
            'raw_response' => $result,
            'parsed_response' => json_decode($result, true),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test get_attlog with parsed response
     */
    public function getAttlogParsed(): ResponseInterface
    {
        $transId = $this->request->getGet('trans_id') ?? '1';
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-7 days'));
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');

        $result = $this->curlClient->getAttlogParsed($transId, $startDate, $endDate);

        return $this->response->setJSON([
            'pdf_curl_example' => 'get_attlog_parsed',
            'description' => 'Parsed response from exact cURL implementation',
            'parameters' => [
                'trans_id' => $transId,
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test current week attendance logs
     */
    public function getAttlogCurrentWeek(): ResponseInterface
    {
        $result = $this->curlClient->getAttlogCurrent('1', 7);

        return $this->response->setJSON([
            'pdf_curl_example' => 'get_attlog_current_week',
            'description' => 'Last 7 days attendance logs using exact cURL implementation',
            'date_range' => [
                'start_date' => date('Y-m-d', strtotime('-7 days')),
                'end_date' => date('Y-m-d')
            ],
            'raw_response' => $result,
            'parsed_response' => json_decode($result, true),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test device info using cURL
     */
    public function getDeviceInfo(): ResponseInterface
    {
        $result = $this->curlClient->getDeviceInfo();

        return $this->response->setJSON([
            'pdf_curl_example' => 'get_device_info',
            'description' => 'Device info using exact cURL pattern',
            'raw_response' => $result,
            'parsed_response' => json_decode($result, true),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test user info using cURL
     */
    public function getUserInfo(): ResponseInterface
    {
        $userPin = $this->request->getGet('user_pin');
        $result = $this->curlClient->getUserInfo($userPin);

        return $this->response->setJSON([
            'pdf_curl_example' => 'get_userinfo',
            'description' => 'User info using exact cURL pattern',
            'user_pin' => $userPin,
            'raw_response' => $result,
            'parsed_response' => json_decode($result, true),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test set user info using cURL
     */
    public function setUserInfo(): ResponseInterface
    {
        $userData = $this->request->getJSON(true) ?: [
            'pin' => '999',
            'name' => 'cURL Test User',
            'privilege' => '0',
            'password' => '',
            'group_id' => '1',
            'user_id' => '999',
            'card_number' => '0'
        ];

        $result = $this->curlClient->setUserInfo($userData);

        return $this->response->setJSON([
            'pdf_curl_example' => 'set_userinfo',
            'description' => 'Set user info using exact cURL pattern',
            'user_data_sent' => $userData,
            'raw_response' => $result,
            'parsed_response' => json_decode($result, true),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Test set time using cURL
     */
    public function setTime(): ResponseInterface
    {
        $datetime = $this->request->getPost('datetime');
        $result = $this->curlClient->setTime($datetime);

        return $this->response->setJSON([
            'pdf_curl_example' => 'set_time',
            'description' => 'Set device time using exact cURL pattern',
            'datetime_sent' => $datetime ?: date('Y-m-d H:i:s'),
            'raw_response' => $result,
            'parsed_response' => json_decode($result, true),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Complete cURL test suite
     */
    public function testSuite(): ResponseInterface
    {
        $tests = [];

        // Test 1: Get Attlog
        $tests['get_attlog'] = [
            'description' => 'Get attendance logs (last 7 days)',
            'raw_response' => $this->curlClient->getAttlogCurrent('1', 7)
        ];

        // Test 2: Get Device Info
        $tests['get_device_info'] = [
            'description' => 'Get device information',
            'raw_response' => $this->curlClient->getDeviceInfo()
        ];

        // Test 3: Get User Info
        $tests['get_user_info'] = [
            'description' => 'Get all users information',
            'raw_response' => $this->curlClient->getUserInfo()
        ];

        // Test 4: Set Time
        $tests['set_time'] = [
            'description' => 'Set device time to current time',
            'raw_response' => $this->curlClient->setTime()
        ];

        // Parse all responses
        foreach ($tests as $key => &$test) {
            $test['parsed_response'] = json_decode($test['raw_response'], true);
            $test['success'] = $test['parsed_response'] !== null;
        }

        return $this->response->setJSON([
            'pdf_curl_test_suite' => 'Complete cURL Implementation Test',
            'description' => 'Tests all major endpoints using exact PDF cURL implementation',
            'configuration' => $this->curlClient->getConfig(),
            'tests' => $tests,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Show cURL configuration
     */
    public function config(): ResponseInterface
    {
        return $this->response->setJSON([
            'curl_configuration' => $this->curlClient->getConfig(),
            'environment_variables' => [
                'fingerspot.apiToken' => env('fingerspot.apiToken') ? 'Set' : 'Not Set',
                'fingerspot.cloudId' => env('fingerspot.cloudId') ? 'Set' : 'Not Set',
                'fingerspot.baseUrl' => env('fingerspot.baseUrl') ? 'Set' : 'Not Set'
            ],
            'curl_options_used' => [
                'CURLOPT_SSL_VERIFYHOST' => 0,
                'CURLOPT_SSL_VERIFYPEER' => 0,
                'CURLOPT_POST' => 1,
                'CURLOPT_RETURNTRANSFER' => 1,
                'CURLOPT_FOLLOWLOCATION' => 1,
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer [api_token]'
            ],
            'exact_pdf_implementation' => true,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Raw cURL test - exactly as in PDF
     */
    public function rawCurlTest(): ResponseInterface
    {
        // Exact implementation from your example
        $url = 'https://developer.fingerspot.io/api/get_attlog';
        $data = json_encode([
            "trans_id" => "1",
            "cloud_id" => env('fingerspot.cloudId'),
            "start_date" => date('Y-m-d', strtotime('-7 days')),
            "end_date" => date('Y-m-d')
        ]);
        $authorization = "Authorization: Bearer " . env('fingerspot.apiToken');

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            $authorization
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);

        $result = curl_exec($ch);
        curl_close($ch);

        return $this->response->setJSON([
            'raw_curl_test' => 'Exact PDF Implementation',
            'description' => 'This is the exact cURL code from your example',
            'code_used' => [
                'url' => $url,
                'data' => $data,
                'authorization' => substr($authorization, 0, 30) . '...',
                'curl_options' => [
                    'SSL_VERIFYHOST' => 0,
                    'SSL_VERIFYPEER' => 0,
                    'POST' => 1,
                    'RETURNTRANSFER' => 1,
                    'FOLLOWLOCATION' => 1
                ]
            ],
            'raw_response' => $result,
            'parsed_response' => json_decode($result, true),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
