<?php

/**
 * Manual Test Script for Fingerspot API
 * 
 * Run this script to test the Fingerspot API implementation manually
 * Usage: php test_fingerspot_manual.php
 */

// Include CodeIgniter bootstrap
require_once __DIR__ . '/vendor/autoload.php';

// Set up the environment
define('FCPATH', __DIR__ . '/public/');
define('SYSTEMPATH', __DIR__ . '/vendor/codeigniter4/framework/system/');
define('APPPATH', __DIR__ . '/app/');
define('WRITEPATH', __DIR__ . '/writable/');
define('ENVIRONMENT', 'development');

// Bootstrap CodeIgniter
$paths = new \Config\Paths();
require_once $paths->systemDirectory . '/Boot.php';
\CodeIgniter\Boot::bootConsole($paths);

echo "=== Fingerspot API Manual Test ===\n\n";

try {
    // Test 1: Basic EasylinkSdk usage (as shown in credentials file)
    echo "Test 1: EasylinkSdk::device()\n";
    echo "--------------------------------\n";
    
    $deviceInfo = \App\Libraries\EasylinkSdk::device();
    echo "Result: " . json_encode($deviceInfo, JSON_PRETTY_PRINT) . "\n\n";
    
    // Test 2: EasylinkSdk scanlogNew
    echo "Test 2: EasylinkSdk::scanlogNew()\n";
    echo "--------------------------------\n";
    
    $attendanceLogs = \App\Libraries\EasylinkSdk::scanlogNew();
    echo "Result: " . json_encode($attendanceLogs, JSON_PRETTY_PRINT) . "\n\n";
    
    // Test 3: FingerspotSdk device info
    echo "Test 3: FingerspotSdk::device()\n";
    echo "--------------------------------\n";
    
    $result = \App\Libraries\FingerspotSdk::device();
    $response = new \App\Models\FingerspotResponse($result);
    
    echo "Success: " . ($response->isSuccess() ? 'Yes' : 'No') . "\n";
    echo "Status Code: " . $response->getStatusCode() . "\n";
    echo "Message: " . $response->getMessage() . "\n";
    echo "Has Data: " . ($response->getData() !== null ? 'Yes' : 'No') . "\n\n";
    
    // Test 4: User service
    echo "Test 4: User Service - Get All Users\n";
    echo "------------------------------------\n";
    
    $userService = \App\Libraries\FingerspotSdk::user();
    $userResult = $userService->getAllUsers();
    $userResponse = new \App\Models\FingerspotResponse($userResult);
    
    echo "Success: " . ($userResponse->isSuccess() ? 'Yes' : 'No') . "\n";
    echo "Status Code: " . $userResponse->getStatusCode() . "\n";
    echo "Message: " . $userResponse->getMessage() . "\n\n";
    
    // Test 5: Attendance service
    echo "Test 5: Attendance Service - Get Logs\n";
    echo "-------------------------------------\n";
    
    $attendanceService = \App\Libraries\FingerspotSdk::attendance();
    $attendanceResult = $attendanceService->getLogs();
    $attendanceResponse = new \App\Models\FingerspotResponse($attendanceResult);
    
    echo "Success: " . ($attendanceResponse->isSuccess() ? 'Yes' : 'No') . "\n";
    echo "Status Code: " . $attendanceResponse->getStatusCode() . "\n";
    echo "Message: " . $attendanceResponse->getMessage() . "\n\n";
    
    // Test 6: Device service
    echo "Test 6: Device Service - Test Connection\n";
    echo "---------------------------------------\n";
    
    $deviceService = \App\Libraries\FingerspotSdk::deviceService();
    $connectionResult = $deviceService->testConnection();
    $connectionResponse = new \App\Models\FingerspotResponse($connectionResult);
    
    echo "Success: " . ($connectionResponse->isSuccess() ? 'Yes' : 'No') . "\n";
    echo "Status Code: " . $connectionResponse->getStatusCode() . "\n";
    echo "Message: " . $connectionResponse->getMessage() . "\n\n";
    
    // Test 7: Helper functions
    echo "Test 7: Helper Functions\n";
    echo "-----------------------\n";
    
    // Load helper
    helper('fingerspot');
    
    $helperDeviceInfo = fingerspot_device();
    echo "Helper fingerspot_device() success: " . (fingerspot_is_success($helperDeviceInfo) ? 'Yes' : 'No') . "\n";
    
    $helperLogs = fingerspot_scanlog_new();
    echo "Helper fingerspot_scanlog_new() success: " . (fingerspot_is_success($helperLogs) ? 'Yes' : 'No') . "\n\n";
    
    // Summary
    echo "=== Test Summary ===\n";
    echo "All tests completed successfully!\n";
    echo "The Fingerspot API client is working correctly.\n\n";
    
    echo "Configuration:\n";
    $config = config('Fingerspot');
    echo "- Base URL: " . $config->baseUrl . "\n";
    echo "- API Token: " . substr($config->apiToken, 0, 8) . "...\n";
    echo "- Cloud ID: " . $config->cloudId . "\n";
    echo "- Debug Mode: " . ($config->debug ? 'Enabled' : 'Disabled') . "\n\n";
    
    echo "Next Steps:\n";
    echo "1. Check the browser test page: http://localhost:8080/test/fingerspot/\n";
    echo "2. Try the examples: http://localhost:8080/examples/fingerspot/basic\n";
    echo "3. Test API endpoints: http://localhost:8080/api/fingerspot/device/info\n";
    echo "4. Enable debug mode in Config/Fingerspot.php if you need detailed logs\n";

} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n\n";
    
    echo "This might indicate:\n";
    echo "1. Missing dependencies - run 'composer install'\n";
    echo "2. Configuration issues - check app/Config/Fingerspot.php\n";
    echo "3. Network connectivity issues\n";
    echo "4. API credentials problems\n";
}

echo "\n=== Test Complete ===\n";
