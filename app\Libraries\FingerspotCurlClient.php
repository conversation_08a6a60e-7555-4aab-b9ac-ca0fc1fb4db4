<?php

namespace App\Libraries;

/**
 * Fingerspot cURL Client
 * 
 * This class implements the exact cURL code from the PDF documentation
 * using environment variables for API token and cloud_id
 */
class FingerspotCurlClient
{
    protected string $apiToken;
    protected string $cloudId;
    protected string $baseUrl;

    public function __construct()
    {
        // Load from environment variables
        $this->apiToken = env('fingerspot.apiToken', '4FWPFC5UR2M4Y7G6');
        $this->cloudId = env('fingerspot.cloudId', 'C2630450C3233D26');
        $this->baseUrl = env('fingerspot.baseUrl', 'https://developer.fingerspot.io/api');
    }

    /**
     * Get Attlog - Exact implementation from PDF documentation
     * 
     * @param string $transId Transaction ID
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @return string Raw response from API
     */
    public function getAttlog(string $transId = "1", string $startDate = null, string $endDate = null): string
    {
        // Default dates if not provided
        if ($startDate === null) {
            $startDate = date('Y-m-d', strtotime('-7 days'));
        }
        if ($endDate === null) {
            $endDate = date('Y-m-d');
        }

        // Exact URL from PDF documentation
        $url = 'https://developer.fingerspot.io/api/get_attlog';

        // Exact data format from PDF documentation
        $data = json_encode([
            "trans_id" => $transId,
            "cloud_id" => $this->cloudId,
            "start_date" => $startDate,
            "end_date" => $endDate
        ]);
        
        // Exact authorization header from PDF documentation
        $authorization = "Authorization: Bearer " . $this->apiToken;
        
        // Exact cURL implementation from PDF documentation
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            $authorization
        ));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        
        $result = curl_exec($ch);
        curl_close($ch);
        
        return $result;
    }

    /**
     * Get Attlog with current date range
     * 
     * @param string $transId Transaction ID
     * @param int $daysBack Number of days back from today
     * @return string Raw response from API
     */
    public function getAttlogCurrent(string $transId = "1", int $daysBack = 7): string
    {
        $endDate = date('Y-m-d');
        $startDate = date('Y-m-d', strtotime("-{$daysBack} days"));
        
        return $this->getAttlog($transId, $startDate, $endDate);
    }

    /**
     * Get Attlog with parsed JSON response
     * 
     * @param string $transId Transaction ID
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @return array Parsed JSON response
     */
    public function getAttlogParsed(string $transId = "1", string $startDate = null, string $endDate = null): array
    {
        $result = $this->getAttlog($transId, $startDate, $endDate);
        $parsed = json_decode($result, true);
        
        return [
            'raw_response' => $result,
            'parsed_data' => $parsed,
            'success' => $parsed !== null,
            'error' => json_last_error_msg()
        ];
    }

    /**
     * Generic cURL request method following PDF documentation pattern
     * 
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @param string $method HTTP method
     * @return string Raw response
     */
    public function makeRequest(string $endpoint, array $data = [], string $method = 'POST'): string
    {
        $url = rtrim($this->baseUrl, '/') . '/' . ltrim($endpoint, '/');
        
        // Prepare data
        $jsonData = json_encode($data);
        
        // Authorization header
        $authorization = "Authorization: Bearer " . $this->apiToken;
        
        // Initialize cURL following PDF documentation pattern
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            $authorization
        ));
        
        if (strtoupper($method) === 'POST') {
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        } elseif (strtoupper($method) === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        } elseif (strtoupper($method) === 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
        }
        
        $result = curl_exec($ch);
        curl_close($ch);
        
        return $result;
    }

    /**
     * Get device info using PDF documentation pattern
     * 
     * @return string Raw response
     */
    public function getDeviceInfo(): string
    {
        return $this->makeRequest('get_device_info', [
            'cloud_id' => $this->cloudId
        ]);
    }

    /**
     * Get user info using PDF documentation pattern
     * 
     * @param string|null $userPin Specific user PIN or null for all users
     * @return string Raw response
     */
    public function getUserInfo(?string $userPin = null): string
    {
        $data = ['cloud_id' => $this->cloudId];
        if ($userPin) {
            $data['user_pin'] = $userPin;
        }
        
        return $this->makeRequest('get_userinfo', $data);
    }

    /**
     * Set user info using PDF documentation pattern
     * 
     * @param array $userData User data
     * @return string Raw response
     */
    public function setUserInfo(array $userData): string
    {
        $data = array_merge(['cloud_id' => $this->cloudId], $userData);
        return $this->makeRequest('set_userinfo', $data);
    }

    /**
     * Set device time using PDF documentation pattern
     * 
     * @param string|null $datetime Datetime string or null for current time
     * @return string Raw response
     */
    public function setTime(?string $datetime = null): string
    {
        $data = [
            'cloud_id' => $this->cloudId,
            'datetime' => $datetime ?: date('Y-m-d H:i:s')
        ];
        
        return $this->makeRequest('set_time', $data);
    }

    /**
     * Restart device using PDF documentation pattern
     * 
     * @return string Raw response
     */
    public function restartDevice(): string
    {
        return $this->makeRequest('restart_device', [
            'cloud_id' => $this->cloudId
        ]);
    }

    /**
     * Register online using PDF documentation pattern
     * 
     * @param array $registerData Registration data
     * @return string Raw response
     */
    public function registerOnline(array $registerData): string
    {
        $data = array_merge(['cloud_id' => $this->cloudId], $registerData);
        return $this->makeRequest('register_online', $data);
    }

    /**
     * Delete user using PDF documentation pattern
     * 
     * @param string $userPin User PIN to delete
     * @return string Raw response
     */
    public function deleteUser(string $userPin): string
    {
        return $this->makeRequest('delete_user', [
            'cloud_id' => $this->cloudId,
            'user_pin' => $userPin
        ]);
    }

    /**
     * Get current configuration
     * 
     * @return array Configuration details
     */
    public function getConfig(): array
    {
        return [
            'api_token' => substr($this->apiToken, 0, 8) . '...',
            'cloud_id' => $this->cloudId,
            'base_url' => $this->baseUrl
        ];
    }
}
