<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\Fingerspot\AttendanceService;
use App\Libraries\Fingerspot\UserService;
use App\Libraries\Fingerspot\DeviceService;
use App\Models\FingerspotResponse;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Fingerspot Controller
 * 
 * Example controller demonstrating Fingerspot API usage
 */
class FingerspotController extends BaseController
{
    protected AttendanceService $attendanceService;
    protected UserService $userService;
    protected DeviceService $deviceService;

    public function __construct()
    {
        $this->attendanceService = new AttendanceService();
        $this->userService = new UserService();
        $this->deviceService = new DeviceService();
    }

    /**
     * Get device information
     */
    public function deviceInfo(): ResponseInterface
    {
        $result = $this->deviceService->getDeviceInfo();
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Get all attendance logs
     */
    public function attendanceLogs(): ResponseInterface
    {
        $result = $this->attendanceService->getLogs();
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Get new attendance logs
     */
    public function newAttendanceLogs(): ResponseInterface
    {
        $lastScanId = $this->request->getGet('last_scan_id');
        $result = $this->attendanceService->getNewLogs($lastScanId);
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Get attendance logs by date range
     */
    public function attendanceLogsByDate(): ResponseInterface
    {
        $startDate = $this->request->getGet('start_date');
        $endDate = $this->request->getGet('end_date');

        if (!$startDate || !$endDate) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'start_date and end_date parameters are required'
            ])->setStatusCode(400);
        }

        $result = $this->attendanceService->getLogsByDateRange($startDate, $endDate);
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Get all users
     */
    public function users(): ResponseInterface
    {
        $result = $this->userService->getAllUsers();
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Get specific user by PIN
     */
    public function userInfo($pin = null): ResponseInterface
    {
        if (!$pin) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User PIN is required'
            ])->setStatusCode(400);
        }

        $result = $this->userService->getUserInfo($pin);
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Create new user
     */
    public function createUser(): ResponseInterface
    {
        $data = $this->request->getJSON(true);

        if (!isset($data['pin']) || !isset($data['name'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'PIN and name are required'
            ])->setStatusCode(400);
        }

        $result = $this->userService->createUser($data['pin'], $data['name'], $data);
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Update user
     */
    public function updateUser($pin = null): ResponseInterface
    {
        if (!$pin) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User PIN is required'
            ])->setStatusCode(400);
        }

        $data = $this->request->getJSON(true);
        $result = $this->userService->updateUser($pin, $data);
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Delete user
     */
    public function deleteUser($pin = null): ResponseInterface
    {
        if (!$pin) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User PIN is required'
            ])->setStatusCode(400);
        }

        $result = $this->userService->deleteUser($pin);
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Restart device
     */
    public function restartDevice(): ResponseInterface
    {
        $result = $this->deviceService->restartDevice();
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Set device time
     */
    public function setDeviceTime(): ResponseInterface
    {
        $data = $this->request->getJSON(true);
        $datetime = $data['datetime'] ?? null;
        $timezone = $data['timezone'] ?? null;

        $result = $this->deviceService->setTime($datetime, $timezone);
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Test connection to device
     */
    public function testConnection(): ResponseInterface
    {
        $result = $this->deviceService->testConnection();
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }
}
