# Fingerspot API Client for CodeIgniter 4

This is a comprehensive API client implementation for Fingerspot.io Developer API, built specifically for CodeIgniter 4 framework.

## Features

- Complete API client for Fingerspot.io Developer API
- Support for all major operations: device management, user management, attendance logs
- Easy-to-use SDK with static methods
- Comprehensive error handling
- Response models for structured data handling
- Example controllers with real-world usage scenarios

## Installation

All files are already created in your CodeIgniter 4 application. The API client is ready to use.

## Configuration

The API configuration is stored in `app/Config/Fingerspot.php`. Update the credentials as needed:

```php
public string $baseUrl = 'https://developer.fingerspot.io/api';
public string $apiToken = '4FWPFC5UR2M4Y7G6';
public string $cloudId = 'C2630450C3233D26';
```

## Basic Usage

### Using EasylinkSdk (as shown in credentials file)

```php
use App\Libraries\EasylinkSdk;

// Get device information
$deviceInfo = EasylinkSdk::device();

// Get new attendance logs
$attendanceLogs = EasylinkSdk::scanlogNew();
```

### Using FingerspotSdk

```php
use App\Libraries\FingerspotSdk;

// Get device information
$deviceInfo = FingerspotSdk::device();

// Get all attendance logs
$logs = FingerspotSdk::scanlog();

// Get user information
$userInfo = FingerspotSdk::userInfo('12345');

// Create new user
$result = FingerspotSdk::setUser([
    'pin' => '12345',
    'name' => 'John Doe',
    'department' => 'IT'
]);
```

## Advanced Usage

### Using Service Classes

```php
use App\Libraries\Fingerspot\AttendanceService;
use App\Libraries\Fingerspot\UserService;
use App\Libraries\Fingerspot\DeviceService;

// Attendance operations
$attendanceService = new AttendanceService();
$logs = $attendanceService->getLogsByDateRange('2024-01-01', '2024-01-31');
$realTimeData = $attendanceService->getRealTimeData();

// User operations
$userService = new UserService();
$allUsers = $userService->getAllUsers();
$userService->createUser('12345', 'John Doe');
$userService->registerFingerprint('12345', $fingerprintTemplate);

// Device operations
$deviceService = new DeviceService();
$deviceInfo = $deviceService->getDeviceInfo();
$deviceService->restartDevice();
$deviceService->setTime();
```

### Response Handling

```php
use App\Models\FingerspotResponse;

$result = FingerspotSdk::device();
$response = new FingerspotResponse($result);

if ($response->isSuccess()) {
    $data = $response->getData();
    echo "Device info: " . json_encode($data);
} else {
    echo "Error: " . $response->getMessage();
}
```

## API Endpoints

### Device Management
- `GET /api/fingerspot/device/info` - Get device information
- `POST /api/fingerspot/device/restart` - Restart device
- `POST /api/fingerspot/device/time` - Set device time
- `GET /api/fingerspot/device/test` - Test connection

### Attendance Management
- `GET /api/fingerspot/attendance/logs` - Get all attendance logs
- `GET /api/fingerspot/attendance/new` - Get new attendance logs
- `GET /api/fingerspot/attendance/date` - Get logs by date range

### User Management
- `GET /api/fingerspot/users` - Get all users
- `GET /api/fingerspot/users/{pin}` - Get specific user
- `POST /api/fingerspot/users` - Create new user
- `PUT /api/fingerspot/users/{pin}` - Update user
- `DELETE /api/fingerspot/users/{pin}` - Delete user

## Example Usage

### Example 1: Get Device Information
```php
// In your controller
public function getDeviceInfo()
{
    $result = EasylinkSdk::device();
    return $this->response->setJSON($result);
}
```

### Example 2: Get New Attendance Logs
```php
public function getNewLogs()
{
    $lastScanId = $this->request->getGet('last_scan_id');
    $result = EasylinkSdk::scanlogNew($lastScanId);
    return $this->response->setJSON($result);
}
```

### Example 3: Create User with Biometric Registration
```php
public function createUserWithBiometric()
{
    $userService = FingerspotSdk::user();
    
    // Create user
    $createResult = $userService->createUser('12345', 'John Doe');
    
    // Register fingerprint
    $fingerprintResult = $userService->registerFingerprint(
        '12345', 
        $fingerprintTemplate, 
        0 // finger index
    );
    
    return $this->response->setJSON([
        'user_created' => $createResult,
        'fingerprint_registered' => $fingerprintResult
    ]);
}
```

## Available Services

### AttendanceService
- `getLogs()` - Get attendance logs
- `getNewLogs()` - Get new logs since last check
- `getLogsByDateRange()` - Get logs by date range
- `getLogsByUser()` - Get logs for specific user
- `getRealTimeData()` - Get real-time attendance data
- `clearLogs()` - Clear attendance logs
- `getStatistics()` - Get attendance statistics

### UserService
- `getUserInfo()` - Get user information
- `getAllUsers()` - Get all users
- `setUser()` - Set/add user
- `createUser()` - Create new user
- `updateUser()` - Update existing user
- `deleteUser()` - Delete user
- `registerOnline()` - Register biometric templates
- `registerFingerprint()` - Register fingerprint
- `registerFace()` - Register face template
- `registerCard()` - Register card
- `setPassword()` - Set user password

### DeviceService
- `getDeviceInfo()` - Get device information
- `restartDevice()` - Restart device
- `setTime()` - Set device time
- `setTimezone()` - Set device timezone
- `syncTime()` - Sync time with server
- `getStatus()` - Get device status
- `getCapacity()` - Get device capacity
- `getFirmwareVersion()` - Get firmware version
- `clearData()` - Clear device data
- `testConnection()` - Test device connection

## Testing the API

Visit the example endpoints to test the implementation:

- `/examples/fingerspot/basic` - Basic usage example
- `/examples/fingerspot/device-info` - Device information
- `/examples/fingerspot/attendance-filter` - Attendance logs with filters
- `/examples/fingerspot/user-management` - User management operations
- `/examples/fingerspot/device-management` - Device management operations

## Error Handling

The API client includes comprehensive error handling:

```php
try {
    $result = FingerspotSdk::device();
    $response = new FingerspotResponse($result);
    
    if ($response->isFailure()) {
        log_message('error', 'API Error: ' . $response->getMessage());
        // Handle error
    }
} catch (Exception $e) {
    log_message('error', 'Exception: ' . $e->getMessage());
    // Handle exception
}
```

## Debug Mode

Enable debug mode in the configuration to log API requests and responses:

```php
// In app/Config/Fingerspot.php
public bool $debug = true;
```

## Files Structure

```
app/
├── Config/
│   └── Fingerspot.php              # API configuration
├── Libraries/
│   ├── FingerspotApiClient.php     # Main API client
│   ├── FingerspotSdk.php           # Main SDK class
│   ├── EasylinkSdk.php             # Alias for compatibility
│   └── Fingerspot/
│       ├── AttendanceService.php   # Attendance operations
│       ├── UserService.php         # User operations
│       └── DeviceService.php       # Device operations
├── Models/
│   └── FingerspotResponse.php      # Response model
└── Controllers/
    ├── FingerspotController.php    # API controller
    └── FingerspotExampleController.php # Example usage
```

## Support

For issues related to the Fingerspot API itself, please refer to the official Fingerspot.io documentation or contact their support team.
