# Fingerspot API Limitation Guide

## ⚠️ **Critical API Limitation: 2-Day Date Range**

Based on your testing results, the Fingerspot API has a **strict limitation** where the date range between `start_date` and `end_date` **cannot exceed 2 days**.

## 🔍 **What This Means**

- ✅ **Valid**: `start_date: "2024-01-01"`, `end_date: "2024-01-02"` (1 day difference)
- ✅ **Valid**: `start_date: "2024-01-01"`, `end_date: "2024-01-03"` (2 days difference)
- ❌ **Invalid**: `start_date: "2024-01-01"`, `end_date: "2024-01-04"` (3 days difference)

## 🛠 **How We Handle This**

### **1. Automatic Date Range Validation**

The cURL client automatically validates and adjusts date ranges:

```php
// If you request more than 2 days, it automatically adjusts
$curlClient = new FingerspotCurlClient();
$result = $curlClient->getAttlog("1", "2024-01-01", "2024-01-10"); 
// Automatically adjusted to: "2024-01-01" to "2024-01-03" (max 2 days)
```

### **2. Multiple Days Support**

For date ranges longer than 2 days, use the `getAttlogMultipleDays()` method:

```php
$curlClient = new FingerspotCurlClient();
$result = $curlClient->getAttlogMultipleDays("1", "2024-01-01", "2024-01-10");
// Makes multiple API calls:
// Call 1: 2024-01-01 to 2024-01-03
// Call 2: 2024-01-04 to 2024-01-06  
// Call 3: 2024-01-07 to 2024-01-09
// Call 4: 2024-01-10 to 2024-01-10
```

### **3. Helper Functions**

```php
// Single request (max 2 days)
$result = fingerspot_get_attlog_curl("1", "2024-01-01", "2024-01-02");

// Multiple requests for longer periods
$result = fingerspot_get_attlog_multiple_days("1", "2024-01-01", "2024-01-10");
```

## 🧪 **Updated Testing**

### **Web Testing**

All test endpoints now respect the 2-day limitation:

- **Raw cURL Test**: `http://localhost:8080/curl/fingerspot/raw-test`
  - Uses yesterday to today (1 day range)
  
- **2-Day Test**: `http://localhost:8080/curl/fingerspot/get-attlog-2days`
  - Uses last 2 days (maximum allowed)
  
- **Multiple Days Test**: `http://localhost:8080/curl/fingerspot/get-attlog-multiple`
  - Demonstrates chunking for longer periods

### **Command Line Testing**

```bash
# Test with 2-day limitation awareness
php test_curl_implementation.php
```

## 📊 **Response Examples**

### **Single Request (≤2 days)**
```json
{
    "success": true,
    "data": [
        {
            "pin": "123",
            "datetime": "2024-01-01 08:00:00",
            "status": "1"
        }
    ],
    "message": "Success"
}
```

### **Multiple Requests (>2 days)**
```json
{
    "total_chunks": 4,
    "requested_range": "2024-01-01 to 2024-01-10",
    "chunks": [
        {
            "date_range": "2024-01-01 to 2024-01-03",
            "start_date": "2024-01-01",
            "end_date": "2024-01-03",
            "raw_response": "...",
            "parsed_response": { "success": true, "data": [...] }
        },
        {
            "date_range": "2024-01-04 to 2024-01-06",
            "start_date": "2024-01-04", 
            "end_date": "2024-01-06",
            "raw_response": "...",
            "parsed_response": { "success": true, "data": [...] }
        }
    ]
}
```

## 🔧 **Implementation Details**

### **Automatic Adjustment Logic**

```php
// In FingerspotCurlClient::getAttlog()
$start = new \DateTime($startDate);
$end = new \DateTime($endDate);
$diff = $start->diff($end)->days;

if ($diff > 2) {
    // Adjust end date to be max 2 days from start date
    $endDate = $start->add(new \DateInterval('P2D'))->format('Y-m-d');
}
```

### **Chunking Logic**

```php
// In FingerspotCurlClient::getAttlogMultipleDays()
while ($start <= $end) {
    $chunkEnd = clone $start;
    $chunkEnd->add(new \DateInterval('P2D')); // Add 2 days
    
    if ($chunkEnd > $end) {
        $chunkEnd = $end; // Don't exceed requested end date
    }
    
    // Make API call for this chunk
    $chunkResult = $this->getAttlog($transId, $start->format('Y-m-d'), $chunkEnd->format('Y-m-d'));
    
    // Move to next chunk
    $start = clone $chunkEnd;
    $start->add(new \DateInterval('P1D'));
}
```

## 📋 **Best Practices**

### **1. For Recent Data (≤2 days)**
```php
// Use direct method
$result = $curlClient->getAttlog("1", date('Y-m-d', strtotime('-1 day')), date('Y-m-d'));
```

### **2. For Historical Data (>2 days)**
```php
// Use multiple days method
$result = $curlClient->getAttlogMultipleDays("1", "2024-01-01", "2024-01-31");
```

### **3. For Real-time Monitoring**
```php
// Get today's data only
$result = $curlClient->getAttlog("1", date('Y-m-d'), date('Y-m-d'));
```

### **4. For Weekly Reports**
```php
// Use chunking for week data
$result = $curlClient->getAttlogMultipleDays("1", date('Y-m-d', strtotime('-7 days')), date('Y-m-d'));
```

## ⚡ **Performance Considerations**

- **Single Request**: Fast, direct API call
- **Multiple Requests**: Slower due to multiple API calls, but necessary for longer periods
- **Rate Limiting**: Be aware that multiple requests may hit API rate limits
- **Data Processing**: Multiple chunks require additional processing to combine results

## 🎯 **Updated Default Behavior**

All methods now default to safe 2-day ranges:

- **Default start_date**: Yesterday (`date('Y-m-d', strtotime('-1 day'))`)
- **Default end_date**: Today (`date('Y-m-d')`)
- **Automatic validation**: Prevents API errors
- **Chunking available**: For longer periods when needed

## ✅ **Testing Verification**

Your cURL test is now successful because:

1. ✅ **Date range ≤ 2 days**: Uses yesterday to today
2. ✅ **Proper headers**: Content-Type and Authorization
3. ✅ **Environment variables**: API token and cloud_id from .env
4. ✅ **SSL settings**: Verification disabled as per PDF
5. ✅ **JSON format**: Proper request structure

The implementation now fully respects the API's 2-day limitation while providing solutions for longer date ranges! 🎉
