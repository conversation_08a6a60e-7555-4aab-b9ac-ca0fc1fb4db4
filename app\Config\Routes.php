<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Fingerspot API Routes
$routes->group('api/fingerspot', function($routes) {
    // Device routes
    $routes->get('device/info', 'FingerspotController::deviceInfo');
    $routes->post('device/restart', 'FingerspotController::restartDevice');
    $routes->post('device/time', 'FingerspotController::setDeviceTime');
    $routes->get('device/test', 'FingerspotController::testConnection');

    // Attendance routes
    $routes->get('attendance/logs', 'FingerspotController::attendanceLogs');
    $routes->get('attendance/new', 'FingerspotController::newAttendanceLogs');
    $routes->get('attendance/date', 'FingerspotController::attendanceLogsByDate');

    // User routes
    $routes->get('users', 'FingerspotController::users');
    $routes->get('users/(:segment)', 'FingerspotController::userInfo/$1');
    $routes->post('users', 'FingerspotController::createUser');
    $routes->put('users/(:segment)', 'FingerspotController::updateUser/$1');
    $routes->delete('users/(:segment)', 'FingerspotController::deleteUser/$1');
});

// Fingerspot Examples Routes
$routes->group('examples/fingerspot', function($routes) {
    $routes->get('basic', 'FingerspotExampleController::basicExample');
    $routes->get('device-info', 'FingerspotExampleController::getDeviceInfo');
    $routes->get('attendance-filter', 'FingerspotExampleController::getAttendanceLogsWithFilter');
    $routes->get('user-management', 'FingerspotExampleController::userManagementExample');
    $routes->get('device-management', 'FingerspotExampleController::deviceManagementExample');
    $routes->get('realtime', 'FingerspotExampleController::realTimeAttendance');
    $routes->post('register-biometric', 'FingerspotExampleController::registerUserWithBiometric');
    $routes->get('statistics', 'FingerspotExampleController::getAttendanceStatistics');
    $routes->get('bulk', 'FingerspotExampleController::bulkOperations');
    $routes->get('error-handling', 'FingerspotExampleController::errorHandlingExample');
});

// Fingerspot Test Routes
$routes->group('test/fingerspot', function($routes) {
    $routes->get('/', 'FingerspotTestController::index');
    $routes->get('(:segment)', 'FingerspotTestController::testSpecific/$1');
});
