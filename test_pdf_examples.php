<?php

/**
 * PDF Examples Test Script
 * 
 * This script tests the exact examples from the Fingerspot API PDF documentation
 * Usage: php test_pdf_examples.php
 */

// Include CodeIgniter bootstrap
require_once __DIR__ . '/vendor/autoload.php';

// Set up the environment
define('FCPATH', __DIR__ . '/public/');
define('SYSTEMPATH', __DIR__ . '/vendor/codeigniter4/framework/system/');
define('APPPATH', __DIR__ . '/app/');
define('WRITEPATH', __DIR__ . '/writable/');
define('ENVIRONMENT', 'development');

// Bootstrap CodeIgniter
$paths = new \Config\Paths();
require_once $paths->systemDirectory . '/Boot.php';
\CodeIgniter\Boot::bootConsole($paths);

echo "=== Fingerspot API PDF Documentation Examples Test ===\n\n";

try {
    // Load helper
    helper('fingerspot');
    
    echo "Configuration:\n";
    $config = config('Fingerspot');
    echo "- Base URL: " . $config->baseUrl . "\n";
    echo "- API Token: " . substr($config->apiToken, 0, 8) . "...\n";
    echo "- Cloud ID: " . $config->cloudId . "\n\n";
    
    echo "Testing PDF Documentation Examples:\n";
    echo "==================================\n\n";
    
    // PDF Example 1: Device Info
    echo "1. Device Info (EasylinkSdk::device())\n";
    echo "--------------------------------------\n";
    echo "Code: \$deviceInfo = EasylinkSdk::device();\n";
    
    $deviceInfo = \App\Libraries\EasylinkSdk::device();
    $deviceResponse = new \App\Models\FingerspotResponse($deviceInfo);
    
    echo "Success: " . ($deviceResponse->isSuccess() ? 'YES' : 'NO') . "\n";
    echo "Status Code: " . $deviceResponse->getStatusCode() . "\n";
    echo "Message: " . $deviceResponse->getMessage() . "\n";
    echo "Has Data: " . ($deviceResponse->getData() !== null ? 'YES' : 'NO') . "\n";
    if ($deviceResponse->isSuccess() && $deviceResponse->getData()) {
        echo "Sample Data: " . json_encode(array_slice((array)$deviceResponse->getData(), 0, 3)) . "...\n";
    }
    echo "\n";
    
    // PDF Example 2: Get Attlog (Scanlog New)
    echo "2. Get Attlog - Scanlog New (EasylinkSdk::scanlogNew())\n";
    echo "-------------------------------------------------------\n";
    echo "Code: \$attendanceLogs = EasylinkSdk::scanlogNew();\n";
    
    $attendanceLogs = \App\Libraries\EasylinkSdk::scanlogNew();
    $attendanceResponse = new \App\Models\FingerspotResponse($attendanceLogs);
    
    echo "Success: " . ($attendanceResponse->isSuccess() ? 'YES' : 'NO') . "\n";
    echo "Status Code: " . $attendanceResponse->getStatusCode() . "\n";
    echo "Message: " . $attendanceResponse->getMessage() . "\n";
    echo "Has Data: " . ($attendanceResponse->getData() !== null ? 'YES' : 'NO') . "\n";
    if ($attendanceResponse->isSuccess() && is_array($attendanceResponse->getData())) {
        echo "Records Count: " . count($attendanceResponse->getData()) . "\n";
    }
    echo "\n";
    
    // PDF Example 3: Get Userinfo
    echo "3. Get Userinfo (FingerspotSdk::userInfo())\n";
    echo "-------------------------------------------\n";
    echo "Code: \$userInfo = FingerspotSdk::userInfo();\n";
    
    $userInfo = \App\Libraries\FingerspotSdk::userInfo();
    $userResponse = new \App\Models\FingerspotResponse($userInfo);
    
    echo "Success: " . ($userResponse->isSuccess() ? 'YES' : 'NO') . "\n";
    echo "Status Code: " . $userResponse->getStatusCode() . "\n";
    echo "Message: " . $userResponse->getMessage() . "\n";
    echo "Has Data: " . ($userResponse->getData() !== null ? 'YES' : 'NO') . "\n";
    if ($userResponse->isSuccess() && is_array($userResponse->getData())) {
        echo "Users Count: " . count($userResponse->getData()) . "\n";
    }
    echo "\n";
    
    // PDF Example 4: Set Userinfo
    echo "4. Set Userinfo (FingerspotSdk::setUser())\n";
    echo "------------------------------------------\n";
    echo "Code: \$result = FingerspotSdk::setUser(\$userData);\n";
    
    $userData = [
        'pin' => '999',
        'name' => 'PDF Test User',
        'privilege' => '0',
        'password' => '',
        'group_id' => '1',
        'user_id' => '999',
        'card_number' => '0'
    ];
    
    echo "User Data: " . json_encode($userData) . "\n";
    
    $setUserResult = \App\Libraries\FingerspotSdk::setUser($userData);
    $setUserResponse = new \App\Models\FingerspotResponse($setUserResult);
    
    echo "Success: " . ($setUserResponse->isSuccess() ? 'YES' : 'NO') . "\n";
    echo "Status Code: " . $setUserResponse->getStatusCode() . "\n";
    echo "Message: " . $setUserResponse->getMessage() . "\n";
    echo "\n";
    
    // PDF Example 5: Set Time
    echo "5. Set Time (FingerspotSdk::setTime())\n";
    echo "--------------------------------------\n";
    echo "Code: \$result = FingerspotSdk::setTime();\n";
    
    $setTimeResult = \App\Libraries\FingerspotSdk::setTime();
    $setTimeResponse = new \App\Models\FingerspotResponse($setTimeResult);
    
    echo "Success: " . ($setTimeResponse->isSuccess() ? 'YES' : 'NO') . "\n";
    echo "Status Code: " . $setTimeResponse->getStatusCode() . "\n";
    echo "Message: " . $setTimeResponse->getMessage() . "\n";
    echo "\n";
    
    // PDF Example 6: Register Online
    echo "6. Register Online (FingerspotSdk::registerOnline())\n";
    echo "----------------------------------------------------\n";
    echo "Code: \$result = FingerspotSdk::registerOnline(\$registerData);\n";
    
    $registerData = [
        'pin' => '999',
        'template_type' => 'fingerprint',
        'template_data' => 'sample_fingerprint_template_data_for_testing',
        'finger_index' => 0
    ];
    
    echo "Register Data: " . json_encode($registerData) . "\n";
    
    $registerResult = \App\Libraries\FingerspotSdk::registerOnline($registerData);
    $registerResponse = new \App\Models\FingerspotResponse($registerResult);
    
    echo "Success: " . ($registerResponse->isSuccess() ? 'YES' : 'NO') . "\n";
    echo "Status Code: " . $registerResponse->getStatusCode() . "\n";
    echo "Message: " . $registerResponse->getMessage() . "\n";
    echo "\n";
    
    // Summary
    echo "=== PDF Examples Test Summary ===\n";
    $examples = [
        'Device Info' => $deviceResponse->isSuccess(),
        'Get Attlog' => $attendanceResponse->isSuccess(),
        'Get Userinfo' => $userResponse->isSuccess(),
        'Set Userinfo' => $setUserResponse->isSuccess(),
        'Set Time' => $setTimeResponse->isSuccess(),
        'Register Online' => $registerResponse->isSuccess()
    ];
    
    $successCount = count(array_filter($examples));
    $totalCount = count($examples);
    
    foreach ($examples as $name => $success) {
        echo "- " . $name . ": " . ($success ? "✅ SUCCESS" : "❌ FAILED") . "\n";
    }
    
    echo "\nOverall Success Rate: {$successCount}/{$totalCount} (" . round(($successCount/$totalCount)*100, 1) . "%)\n\n";
    
    echo "=== Helper Functions Test ===\n";
    echo "Testing helper functions from fingerspot_helper.php:\n\n";
    
    // Test helper functions
    $helperDeviceInfo = fingerspot_device();
    echo "fingerspot_device(): " . (fingerspot_is_success($helperDeviceInfo) ? "✅ SUCCESS" : "❌ FAILED") . "\n";
    
    $helperScanlog = fingerspot_scanlog_new();
    echo "fingerspot_scanlog_new(): " . (fingerspot_is_success($helperScanlog) ? "✅ SUCCESS" : "❌ FAILED") . "\n";
    
    $helperUserInfo = fingerspot_user_info();
    echo "fingerspot_user_info(): " . (fingerspot_is_success($helperUserInfo) ? "✅ SUCCESS" : "❌ FAILED") . "\n";
    
    echo "\n=== Test URLs ===\n";
    echo "You can also test these examples via web browser:\n";
    echo "- All PDF Examples: http://localhost:8080/pdf/fingerspot/\n";
    echo "- PDF Analysis: http://localhost:8080/pdf/fingerspot/analysis\n";
    echo "- Device Info: http://localhost:8080/pdf/fingerspot/device-info\n";
    echo "- Get Attlog: http://localhost:8080/pdf/fingerspot/get-attlog\n";
    echo "- Get Userinfo: http://localhost:8080/pdf/fingerspot/get-userinfo\n";
    echo "- Interactive Test: http://localhost:8080/fingerspot_test.html\n";
    
    echo "\n=== Notes ===\n";
    echo "- If tests show errors, check your API credentials and network connection\n";
    echo "- Enable debug mode in Config/Fingerspot.php for detailed API logs\n";
    echo "- Ensure your Fingerspot device is online and accessible\n";
    echo "- Some operations (like Set Userinfo) may require specific device permissions\n";

} catch (Exception $e) {
    echo "CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n\n";
    
    echo "This might indicate:\n";
    echo "1. Missing dependencies - run 'composer install'\n";
    echo "2. Configuration issues - check app/Config/Fingerspot.php\n";
    echo "3. Network connectivity issues\n";
    echo "4. API credentials problems\n";
    echo "5. CodeIgniter framework issues\n";
}

echo "\n=== PDF Examples Test Complete ===\n";
