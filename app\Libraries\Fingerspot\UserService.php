<?php

namespace App\Libraries\Fingerspot;

use App\Libraries\FingerspotApiClient;

/**
 * Fingerspot User Service
 * 
 * Service for handling user management operations
 */
class UserService
{
    protected FingerspotApiClient $client;

    public function __construct()
    {
        $this->client = new FingerspotApiClient();
    }

    /**
     * Get user information from device
     * 
     * @param string|null $userPin Specific user PIN, null for all users
     * @return array
     */
    public function getUserInfo(?string $userPin = null): array
    {
        $params = [];
        if ($userPin) {
            $params['user_pin'] = $userPin;
        }

        $endpoint = $this->client->getEndpoint('user_info');
        return $this->client->get($endpoint, $params);
    }

    /**
     * Get all users from device
     * 
     * @return array
     */
    public function getAllUsers(): array
    {
        $endpoint = $this->client->getEndpoint('user_info');
        return $this->client->get($endpoint);
    }

    /**
     * Set/Add user to device
     * 
     * @param array $userData User data
     * @return array
     */
    public function setUser(array $userData): array
    {
        $endpoint = $this->client->getEndpoint('set_user');
        return $this->client->post($endpoint, $userData);
    }

    /**
     * Create a new user with basic information
     * 
     * @param string $pin User PIN
     * @param string $name User name
     * @param array $additionalData Additional user data
     * @return array
     */
    public function createUser(string $pin, string $name, array $additionalData = []): array
    {
        $userData = array_merge([
            'pin' => $pin,
            'name' => $name
        ], $additionalData);

        return $this->setUser($userData);
    }

    /**
     * Update existing user
     * 
     * @param string $pin User PIN
     * @param array $updateData Data to update
     * @return array
     */
    public function updateUser(string $pin, array $updateData): array
    {
        $userData = array_merge(['pin' => $pin], $updateData);
        return $this->setUser($userData);
    }

    /**
     * Delete user from device
     * 
     * @param string $userPin User PIN to delete
     * @return array
     */
    public function deleteUser(string $userPin): array
    {
        $endpoint = $this->client->getEndpoint('delete_user');
        return $this->client->delete($endpoint, ['user_pin' => $userPin]);
    }

    /**
     * Register user online (for biometric templates)
     * 
     * @param array $registrationData Registration data
     * @return array
     */
    public function registerOnline(array $registrationData): array
    {
        $endpoint = $this->client->getEndpoint('register_online');
        return $this->client->post($endpoint, $registrationData);
    }

    /**
     * Register fingerprint for user
     * 
     * @param string $pin User PIN
     * @param string $fingerprintTemplate Fingerprint template data
     * @param int $fingerIndex Finger index (0-9)
     * @return array
     */
    public function registerFingerprint(string $pin, string $fingerprintTemplate, int $fingerIndex = 0): array
    {
        $data = [
            'pin' => $pin,
            'template_type' => 'fingerprint',
            'template_data' => $fingerprintTemplate,
            'finger_index' => $fingerIndex
        ];

        return $this->registerOnline($data);
    }

    /**
     * Register face template for user
     * 
     * @param string $pin User PIN
     * @param string $faceTemplate Face template data
     * @return array
     */
    public function registerFace(string $pin, string $faceTemplate): array
    {
        $data = [
            'pin' => $pin,
            'template_type' => 'face',
            'template_data' => $faceTemplate
        ];

        return $this->registerOnline($data);
    }

    /**
     * Register card for user
     * 
     * @param string $pin User PIN
     * @param string $cardNumber Card number
     * @return array
     */
    public function registerCard(string $pin, string $cardNumber): array
    {
        $data = [
            'pin' => $pin,
            'template_type' => 'card',
            'card_number' => $cardNumber
        ];

        return $this->registerOnline($data);
    }

    /**
     * Set password for user
     * 
     * @param string $pin User PIN
     * @param string $password User password
     * @return array
     */
    public function setPassword(string $pin, string $password): array
    {
        $data = [
            'pin' => $pin,
            'password' => $password
        ];

        return $this->setUser($data);
    }

    /**
     * Get user count from device
     * 
     * @return array
     */
    public function getUserCount(): array
    {
        $endpoint = $this->client->getEndpoint('user_info') . '/count';
        return $this->client->get($endpoint);
    }
}
