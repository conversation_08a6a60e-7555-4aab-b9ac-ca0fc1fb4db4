<?php

namespace App\Models;

/**
 * Fingerspot API Response Model
 * 
 * Model for handling and formatting API responses
 */
class FingerspotResponse
{
    protected bool $success;
    protected int $statusCode;
    protected mixed $data;
    protected string $message;
    protected array $errors;

    public function __construct(array $response)
    {
        $this->success = $response['success'] ?? false;
        $this->statusCode = $response['status_code'] ?? 0;
        $this->data = $response['data'] ?? null;
        $this->message = $response['message'] ?? '';
        $this->errors = $response['errors'] ?? [];
    }

    /**
     * Check if the response was successful
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * Check if the response failed
     */
    public function isFailure(): bool
    {
        return !$this->success;
    }

    /**
     * Get response status code
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Get response data
     */
    public function getData(): mixed
    {
        return $this->data;
    }

    /**
     * Get response message
     */
    public function getMessage(): string
    {
        return $this->message;
    }

    /**
     * Get response errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get data as array
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'status_code' => $this->statusCode,
            'data' => $this->data,
            'message' => $this->message,
            'errors' => $this->errors
        ];
    }

    /**
     * Get data as JSON string
     */
    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_PRETTY_PRINT);
    }

    /**
     * Check if response has specific data key
     */
    public function hasData(string $key): bool
    {
        return is_array($this->data) && isset($this->data[$key]);
    }

    /**
     * Get specific data by key
     */
    public function getDataByKey(string $key, mixed $default = null): mixed
    {
        if (is_array($this->data) && isset($this->data[$key])) {
            return $this->data[$key];
        }
        return $default;
    }

    /**
     * Get first error message
     */
    public function getFirstError(): string
    {
        return !empty($this->errors) ? $this->errors[0] : '';
    }

    /**
     * Check if response has errors
     */
    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }
}
