<?php

namespace App\Libraries;

use App\Libraries\Fingerspot\AttendanceService;
use App\Libraries\Fingerspot\UserService;
use App\Libraries\Fingerspot\DeviceService;

/**
 * Fingerspot SDK
 * 
 * Main SDK class that provides access to all Fingerspot services
 * This class follows the pattern shown in the credentials file
 */
class FingerspotSdk
{
    protected static ?AttendanceService $attendanceService = null;
    protected static ?UserService $userService = null;
    protected static ?DeviceService $deviceService = null;

    /**
     * Get device information
     * 
     * @return array
     */
    public static function device(): array
    {
        if (self::$deviceService === null) {
            self::$deviceService = new DeviceService();
        }

        return self::$deviceService->getDeviceInfo();
    }

    /**
     * Get new attendance logs (scanlog new)
     * 
     * @param string|null $lastScanId Last scan ID
     * @return array
     */
    public static function scanlogNew(?string $lastScanId = null): array
    {
        if (self::$attendanceService === null) {
            self::$attendanceService = new AttendanceService();
        }

        return self::$attendanceService->getNewLogs($lastScanId);
    }

    /**
     * Get all attendance logs
     * 
     * @param array $params Optional parameters
     * @return array
     */
    public static function scanlog(array $params = []): array
    {
        if (self::$attendanceService === null) {
            self::$attendanceService = new AttendanceService();
        }

        return self::$attendanceService->getLogs($params);
    }

    /**
     * Get user information
     * 
     * @param string|null $userPin User PIN, null for all users
     * @return array
     */
    public static function userInfo(?string $userPin = null): array
    {
        if (self::$userService === null) {
            self::$userService = new UserService();
        }

        return self::$userService->getUserInfo($userPin);
    }

    /**
     * Set/Add user to device
     * 
     * @param array $userData User data
     * @return array
     */
    public static function setUser(array $userData): array
    {
        if (self::$userService === null) {
            self::$userService = new UserService();
        }

        return self::$userService->setUser($userData);
    }

    /**
     * Delete user from device
     * 
     * @param string $userPin User PIN
     * @return array
     */
    public static function deleteUser(string $userPin): array
    {
        if (self::$userService === null) {
            self::$userService = new UserService();
        }

        return self::$userService->deleteUser($userPin);
    }

    /**
     * Set device time
     * 
     * @param string|null $datetime Datetime string
     * @param string|null $timezone Timezone string
     * @return array
     */
    public static function setTime(?string $datetime = null, ?string $timezone = null): array
    {
        if (self::$deviceService === null) {
            self::$deviceService = new DeviceService();
        }

        return self::$deviceService->setTime($datetime, $timezone);
    }

    /**
     * Restart device
     * 
     * @return array
     */
    public static function restartDevice(): array
    {
        if (self::$deviceService === null) {
            self::$deviceService = new DeviceService();
        }

        return self::$deviceService->restartDevice();
    }

    /**
     * Register user online
     * 
     * @param array $registrationData Registration data
     * @return array
     */
    public static function registerOnline(array $registrationData): array
    {
        if (self::$userService === null) {
            self::$userService = new UserService();
        }

        return self::$userService->registerOnline($registrationData);
    }

    /**
     * Get attendance service instance
     * 
     * @return AttendanceService
     */
    public static function attendance(): AttendanceService
    {
        if (self::$attendanceService === null) {
            self::$attendanceService = new AttendanceService();
        }

        return self::$attendanceService;
    }

    /**
     * Get user service instance
     * 
     * @return UserService
     */
    public static function user(): UserService
    {
        if (self::$userService === null) {
            self::$userService = new UserService();
        }

        return self::$userService;
    }

    /**
     * Get device service instance
     * 
     * @return DeviceService
     */
    public static function deviceService(): DeviceService
    {
        if (self::$deviceService === null) {
            self::$deviceService = new DeviceService();
        }

        return self::$deviceService;
    }
}
