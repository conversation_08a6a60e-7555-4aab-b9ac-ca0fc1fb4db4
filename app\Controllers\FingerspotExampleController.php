<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\EasylinkSdk;
use App\Libraries\FingerspotSdk;
use App\Models\FingerspotResponse;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Fingerspot Example Controller
 * 
 * Comprehensive examples of using the Fingerspot API SDK
 */
class FingerspotExampleController extends BaseController
{
    /**
     * Example 1: Basic usage as shown in credentials file
     */
    public function basicExample(): ResponseInterface
    {
        // Using EasylinkSdk as shown in the credentials file
        $deviceInfo = EasylinkSdk::device();
        $attendanceLogs = EasylinkSdk::scanlogNew();

        return $this->response->setJSON([
            'device_info' => $deviceInfo,
            'attendance_logs' => $attendanceLogs
        ]);
    }

    /**
     * Example 2: Get device information
     */
    public function getDeviceInfo(): ResponseInterface
    {
        $result = FingerspotSdk::device();
        $response = new FingerspotResponse($result);

        if ($response->isSuccess()) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Device info retrieved successfully',
                'data' => $response->getData()
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => $response->getMessage(),
            'errors' => $response->getErrors()
        ])->setStatusCode(400);
    }

    /**
     * Example 3: Get attendance logs with date filter
     */
    public function getAttendanceLogsWithFilter(): ResponseInterface
    {
        $startDate = $this->request->getGet('start_date') ?? date('Y-m-d', strtotime('-7 days'));
        $endDate = $this->request->getGet('end_date') ?? date('Y-m-d');

        $attendanceService = FingerspotSdk::attendance();
        $result = $attendanceService->getLogsByDateRange($startDate, $endDate);
        $response = new FingerspotResponse($result);

        return $this->response->setJSON($response->toArray());
    }

    /**
     * Example 4: User management operations
     */
    public function userManagementExample(): ResponseInterface
    {
        $userService = FingerspotSdk::user();
        
        // Get all users
        $allUsers = $userService->getAllUsers();
        
        // Create a new user
        $newUserResult = $userService->createUser('12345', 'John Doe', [
            'department' => 'IT',
            'position' => 'Developer'
        ]);

        // Get specific user
        $userInfo = $userService->getUserInfo('12345');

        return $this->response->setJSON([
            'all_users' => $allUsers,
            'new_user_result' => $newUserResult,
            'user_info' => $userInfo
        ]);
    }

    /**
     * Example 5: Device management operations
     */
    public function deviceManagementExample(): ResponseInterface
    {
        $deviceService = FingerspotSdk::deviceService();
        
        // Get device status
        $status = $deviceService->getStatus();
        
        // Get device capacity
        $capacity = $deviceService->getCapacity();
        
        // Test connection
        $connectionTest = $deviceService->testConnection();
        
        // Sync time
        $timeSync = $deviceService->syncTime();

        return $this->response->setJSON([
            'status' => $status,
            'capacity' => $capacity,
            'connection_test' => $connectionTest,
            'time_sync' => $timeSync
        ]);
    }

    /**
     * Example 6: Real-time attendance monitoring
     */
    public function realTimeAttendance(): ResponseInterface
    {
        $attendanceService = FingerspotSdk::attendance();
        
        // Get real-time data
        $realTimeData = $attendanceService->getRealTimeData();
        
        // Get new logs since last check
        $lastScanId = $this->request->getGet('last_scan_id');
        $newLogs = $attendanceService->getNewLogs($lastScanId);

        return $this->response->setJSON([
            'real_time_data' => $realTimeData,
            'new_logs' => $newLogs
        ]);
    }

    /**
     * Example 7: User registration with biometric
     */
    public function registerUserWithBiometric(): ResponseInterface
    {
        $data = $this->request->getJSON(true);
        
        if (!isset($data['pin']) || !isset($data['name'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'PIN and name are required'
            ])->setStatusCode(400);
        }

        $userService = FingerspotSdk::user();
        
        // Create user first
        $createResult = $userService->createUser($data['pin'], $data['name']);
        
        if (!$createResult['success']) {
            return $this->response->setJSON($createResult)->setStatusCode(400);
        }

        // Register fingerprint if provided
        if (isset($data['fingerprint_template'])) {
            $fingerprintResult = $userService->registerFingerprint(
                $data['pin'], 
                $data['fingerprint_template'],
                $data['finger_index'] ?? 0
            );
        }

        // Register face if provided
        if (isset($data['face_template'])) {
            $faceResult = $userService->registerFace($data['pin'], $data['face_template']);
        }

        // Register card if provided
        if (isset($data['card_number'])) {
            $cardResult = $userService->registerCard($data['pin'], $data['card_number']);
        }

        return $this->response->setJSON([
            'user_created' => $createResult,
            'fingerprint_registered' => $fingerprintResult ?? null,
            'face_registered' => $faceResult ?? null,
            'card_registered' => $cardResult ?? null
        ]);
    }

    /**
     * Example 8: Attendance statistics
     */
    public function getAttendanceStatistics(): ResponseInterface
    {
        $attendanceService = FingerspotSdk::attendance();
        
        $params = [
            'start_date' => $this->request->getGet('start_date') ?? date('Y-m-01'),
            'end_date' => $this->request->getGet('end_date') ?? date('Y-m-d'),
            'user_pin' => $this->request->getGet('user_pin')
        ];

        $statistics = $attendanceService->getStatistics($params);

        return $this->response->setJSON($statistics);
    }

    /**
     * Example 9: Bulk operations
     */
    public function bulkOperations(): ResponseInterface
    {
        $userService = FingerspotSdk::user();
        $attendanceService = FingerspotSdk::attendance();
        
        // Get all users
        $allUsersResult = $userService->getAllUsers();
        $allUsers = $allUsersResult['data'] ?? [];
        
        $results = [];
        
        // Process each user
        foreach ($allUsers as $user) {
            if (isset($user['pin'])) {
                // Get attendance logs for this user
                $userLogs = $attendanceService->getLogsByUser($user['pin']);
                $results[] = [
                    'user' => $user,
                    'logs_count' => count($userLogs['data'] ?? [])
                ];
            }
        }

        return $this->response->setJSON([
            'total_users' => count($allUsers),
            'user_attendance_summary' => $results
        ]);
    }

    /**
     * Example 10: Error handling demonstration
     */
    public function errorHandlingExample(): ResponseInterface
    {
        try {
            // Attempt to get user with invalid PIN
            $result = FingerspotSdk::userInfo('invalid_pin');
            $response = new FingerspotResponse($result);
            
            if ($response->isFailure()) {
                return $this->response->setJSON([
                    'error_handled' => true,
                    'status_code' => $response->getStatusCode(),
                    'message' => $response->getMessage(),
                    'has_errors' => $response->hasErrors(),
                    'first_error' => $response->getFirstError()
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'data' => $response->getData()
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'exception_caught' => true,
                'message' => $e->getMessage()
            ])->setStatusCode(500);
        }
    }
}
