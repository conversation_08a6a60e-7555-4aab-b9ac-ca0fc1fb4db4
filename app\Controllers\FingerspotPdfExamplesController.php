<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\EasylinkSdk;
use App\Libraries\FingerspotSdk;
use App\Models\FingerspotResponse;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Fingerspot PDF Examples Controller
 * 
 * This controller implements the exact examples from the PDF documentation
 * to ensure compatibility with the documented API usage patterns
 */
class FingerspotPdfExamplesController extends BaseController
{
    /**
     * PDF Example 1: Device Info
     * Get device information as shown in PDF documentation
     */
    public function deviceInfo(): ResponseInterface
    {
        // Using EasylinkSdk as shown in credentials file
        $deviceInfo = EasylinkSdk::device();
        
        return $this->response->setJSON([
            'pdf_example' => 'Device Info',
            'description' => 'Get device information using EasylinkSdk::device()',
            'code_example' => '$deviceInfo = EasylinkSdk::device();',
            'result' => $deviceInfo,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * PDF Example 2: Get Attlog (Attendance Logs)
     * Get attendance logs as shown in PDF documentation
     */
    public function getAttlog(): ResponseInterface
    {
        // Get new attendance logs
        $attendanceLogs = EasylinkSdk::scanlogNew();
        
        return $this->response->setJSON([
            'pdf_example' => 'Get Attlog',
            'description' => 'Get new attendance logs using EasylinkSdk::scanlogNew()',
            'code_example' => '$attendanceLogs = EasylinkSdk::scanlogNew();',
            'result' => $attendanceLogs,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * PDF Example 3: Get Userinfo
     * Get user information as shown in PDF documentation
     */
    public function getUserinfo(): ResponseInterface
    {
        // Get all users
        $userInfo = FingerspotSdk::userInfo();
        
        return $this->response->setJSON([
            'pdf_example' => 'Get Userinfo',
            'description' => 'Get user information using FingerspotSdk::userInfo()',
            'code_example' => '$userInfo = FingerspotSdk::userInfo();',
            'result' => $userInfo,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * PDF Example 4: Set Userinfo
     * Set/Add user information as shown in PDF documentation
     */
    public function setUserinfo(): ResponseInterface
    {
        // Example user data as per PDF documentation
        $userData = [
            'pin' => '999',
            'name' => 'Test User from PDF Example',
            'privilege' => '0',
            'password' => '',
            'group_id' => '1',
            'user_id' => '999',
            'card_number' => '0'
        ];
        
        $result = FingerspotSdk::setUser($userData);
        
        return $this->response->setJSON([
            'pdf_example' => 'Set Userinfo',
            'description' => 'Set user information using FingerspotSdk::setUser()',
            'code_example' => '$result = FingerspotSdk::setUser($userData);',
            'user_data_sent' => $userData,
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * PDF Example 5: Set Time
     * Set device time as shown in PDF documentation
     */
    public function setTime(): ResponseInterface
    {
        $result = FingerspotSdk::setTime();
        
        return $this->response->setJSON([
            'pdf_example' => 'Set Time',
            'description' => 'Set device time using FingerspotSdk::setTime()',
            'code_example' => '$result = FingerspotSdk::setTime();',
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * PDF Example 6: Restart Device
     * Restart device as shown in PDF documentation
     */
    public function restartDevice(): ResponseInterface
    {
        $result = FingerspotSdk::restartDevice();
        
        return $this->response->setJSON([
            'pdf_example' => 'Restart Device',
            'description' => 'Restart device using FingerspotSdk::restartDevice()',
            'code_example' => '$result = FingerspotSdk::restartDevice();',
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * PDF Example 7: Register Online
     * Register biometric template online as shown in PDF documentation
     */
    public function registerOnline(): ResponseInterface
    {
        $registerData = [
            'pin' => '999',
            'template_type' => 'fingerprint',
            'template_data' => 'sample_fingerprint_template_data_for_testing',
            'finger_index' => 0
        ];
        
        $result = FingerspotSdk::registerOnline($registerData);
        
        return $this->response->setJSON([
            'pdf_example' => 'Register Online',
            'description' => 'Register biometric template using FingerspotSdk::registerOnline()',
            'code_example' => '$result = FingerspotSdk::registerOnline($registerData);',
            'register_data_sent' => $registerData,
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * PDF Example 8: Delete User
     * Delete user as shown in PDF documentation
     */
    public function deleteUser(): ResponseInterface
    {
        $userPin = '999'; // Test user PIN
        $result = FingerspotSdk::deleteUser($userPin);
        
        return $this->response->setJSON([
            'pdf_example' => 'Delete User',
            'description' => 'Delete user using FingerspotSdk::deleteUser()',
            'code_example' => '$result = FingerspotSdk::deleteUser("999");',
            'user_pin_deleted' => $userPin,
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Complete PDF Examples Test Suite
     * Run all PDF examples in sequence
     */
    public function allExamples(): ResponseInterface
    {
        $examples = [];
        
        // 1. Device Info
        $examples['device_info'] = EasylinkSdk::device();
        
        // 2. Get Attlog
        $examples['get_attlog'] = EasylinkSdk::scanlogNew();
        
        // 3. Get Userinfo
        $examples['get_userinfo'] = FingerspotSdk::userInfo();
        
        // 4. Set Userinfo
        $userData = [
            'pin' => '999',
            'name' => 'PDF Test User',
            'privilege' => '0',
            'password' => '',
            'group_id' => '1',
            'user_id' => '999',
            'card_number' => '0'
        ];
        $examples['set_userinfo'] = FingerspotSdk::setUser($userData);
        
        // 5. Set Time
        $examples['set_time'] = FingerspotSdk::setTime();
        
        // 6. Register Online
        $registerData = [
            'pin' => '999',
            'template_type' => 'fingerprint',
            'template_data' => 'sample_template_data',
            'finger_index' => 0
        ];
        $examples['register_online'] = FingerspotSdk::registerOnline($registerData);
        
        // 7. Restart Device (commented out to avoid actually restarting)
        // $examples['restart_device'] = FingerspotSdk::restartDevice();
        
        // 8. Delete User (commented out to avoid deleting test user)
        // $examples['delete_user'] = FingerspotSdk::deleteUser('999');
        
        return $this->response->setJSON([
            'pdf_examples_test_suite' => 'All PDF Documentation Examples',
            'description' => 'Complete test of all API examples from PDF documentation',
            'examples_tested' => array_keys($examples),
            'results' => $examples,
            'timestamp' => date('Y-m-d H:i:s'),
            'note' => 'Restart device and delete user examples are commented out for safety'
        ]);
    }

    /**
     * PDF Examples with Response Analysis
     * Analyze responses using FingerspotResponse model
     */
    public function examplesWithAnalysis(): ResponseInterface
    {
        $analysis = [];
        
        // Test each example and analyze response
        $examples = [
            'device_info' => EasylinkSdk::device(),
            'get_attlog' => EasylinkSdk::scanlogNew(),
            'get_userinfo' => FingerspotSdk::userInfo(),
            'set_time' => FingerspotSdk::setTime()
        ];
        
        foreach ($examples as $name => $result) {
            $response = new FingerspotResponse($result);
            $analysis[$name] = [
                'success' => $response->isSuccess(),
                'status_code' => $response->getStatusCode(),
                'message' => $response->getMessage(),
                'has_data' => $response->getData() !== null,
                'data_count' => is_array($response->getData()) ? count($response->getData()) : 0,
                'raw_result' => $result
            ];
        }
        
        return $this->response->setJSON([
            'pdf_examples_analysis' => 'PDF Examples with Response Analysis',
            'analysis' => $analysis,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
