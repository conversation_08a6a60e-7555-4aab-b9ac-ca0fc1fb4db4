<?php

/**
 * Test Exact cURL Implementation
 * 
 * This script tests the exact cURL implementation from the PDF documentation
 * using environment variables for API token and cloud_id
 */

// Include CodeIgniter bootstrap
require_once __DIR__ . '/vendor/autoload.php';

// Set up the environment
define('FCPATH', __DIR__ . '/public/');
define('SYSTEMPATH', __DIR__ . '/vendor/codeigniter4/framework/system/');
define('APPPATH', __DIR__ . '/app/');
define('WRITEPATH', __DIR__ . '/writable/');
define('ENVIRONMENT', 'development');

// Bootstrap CodeIgniter
$paths = new \Config\Paths();
require_once $paths->systemDirectory . '/Boot.php';
\CodeIgniter\Boot::bootConsole($paths);

echo "=== Exact cURL Implementation Test ===\n\n";

try {
    // Load helper
    helper('fingerspot');
    
    echo "Environment Variables Check:\n";
    echo "===========================\n";
    echo "fingerspot.apiToken: " . (env('fingerspot.apiToken') ? 'SET (' . substr(env('fingerspot.apiToken'), 0, 8) . '...)' : 'NOT SET') . "\n";
    echo "fingerspot.cloudId: " . (env('fingerspot.cloudId') ? 'SET (' . env('fingerspot.cloudId') . ')' : 'NOT SET') . "\n";
    echo "fingerspot.baseUrl: " . (env('fingerspot.baseUrl') ? 'SET (' . env('fingerspot.baseUrl') . ')' : 'NOT SET') . "\n\n";
    
    echo "Testing Exact cURL Implementation from PDF:\n";
    echo "==========================================\n\n";
    
    // Test 1: Raw cURL implementation exactly as provided
    echo "1. Raw cURL Test (Exact PDF Implementation)\n";
    echo "-------------------------------------------\n";
    
    // Your exact code with environment variables
    $url = 'https://developer.fingerspot.io/api/get_attlog';
    $data = json_encode([
        "trans_id" => "1",
        "cloud_id" => env('fingerspot.cloudId'),
        "start_date" => date('Y-m-d', strtotime('-1 day')), // API allows max 2 days range
        "end_date" => date('Y-m-d')
    ]);
    $authorization = "Authorization: Bearer " . env('fingerspot.apiToken');
    
    echo "URL: $url\n";
    echo "Data: $data\n";
    echo "Authorization: " . substr($authorization, 0, 30) . "...\n\n";
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        $authorization
    ));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "cURL Error: " . ($curlError ?: 'None') . "\n";
    echo "Response Length: " . strlen($result) . " bytes\n";
    
    $parsedResult = json_decode($result, true);
    if ($parsedResult !== null) {
        echo "JSON Parse: SUCCESS\n";
        echo "Response Keys: " . implode(', ', array_keys($parsedResult)) . "\n";
        if (isset($parsedResult['data']) && is_array($parsedResult['data'])) {
            echo "Data Records: " . count($parsedResult['data']) . "\n";
        }
    } else {
        echo "JSON Parse: FAILED\n";
        echo "Raw Response: " . substr($result, 0, 200) . "...\n";
    }
    echo "\n";
    
    // Test 2: Using the cURL client class
    echo "2. cURL Client Class Test\n";
    echo "------------------------\n";
    
    $curlClient = new \App\Libraries\FingerspotCurlClient();
    $clientResult = $curlClient->getAttlog("1", date('Y-m-d', strtotime('-1 day')), date('Y-m-d'));
    
    echo "Client Response Length: " . strlen($clientResult) . " bytes\n";
    echo "Same as Raw cURL: " . ($result === $clientResult ? 'YES' : 'NO') . "\n";
    
    $clientParsed = json_decode($clientResult, true);
    if ($clientParsed !== null) {
        echo "Client JSON Parse: SUCCESS\n";
        if (isset($clientParsed['data']) && is_array($clientParsed['data'])) {
            echo "Client Data Records: " . count($clientParsed['data']) . "\n";
        }
    } else {
        echo "Client JSON Parse: FAILED\n";
    }
    echo "\n";
    
    // Test 3: Using helper functions
    echo "3. Helper Function Test\n";
    echo "----------------------\n";
    
    $helperResult = fingerspot_get_attlog_curl("1", date('Y-m-d', strtotime('-1 day')), date('Y-m-d'));
    echo "Helper Response Length: " . strlen($helperResult) . " bytes\n";
    echo "Same as Raw cURL: " . ($result === $helperResult ? 'YES' : 'NO') . "\n";

    $helperParsed = fingerspot_get_attlog_curl_parsed("1", date('Y-m-d', strtotime('-1 day')), date('Y-m-d'));
    echo "Helper Parsed Success: " . (isset($helperParsed['success']) ? ($helperParsed['success'] ? 'YES' : 'NO') : 'UNKNOWN') . "\n";
    echo "\n";

    // Test 4: Multiple Days Test (API 2-day limitation handling)
    echo "4. Multiple Days Test (API 2-day Limitation Handling)\n";
    echo "-----------------------------------------------------\n";

    $multipleDaysResult = $curlClient->getAttlogMultipleDays("1", date('Y-m-d', strtotime('-7 days')), date('Y-m-d'));
    echo "Multiple Days Request: 7 days (should be chunked into multiple 2-day requests)\n";
    echo "Total Chunks: " . $multipleDaysResult['total_chunks'] . "\n";
    echo "Requested Range: " . $multipleDaysResult['requested_range'] . "\n";

    foreach ($multipleDaysResult['chunks'] as $index => $chunk) {
        echo "Chunk " . ($index + 1) . ": " . $chunk['date_range'];
        $chunkParsed = $chunk['parsed_response'];
        if ($chunkParsed && isset($chunkParsed['data'])) {
            echo " - Records: " . (is_array($chunkParsed['data']) ? count($chunkParsed['data']) : 0);
        }
        echo "\n";
    }
    echo "\n";

    // Test 5: Configuration verification
    echo "5. Configuration Verification\n";
    echo "-----------------------------\n";
    
    $config = $curlClient->getConfig();
    echo "API Token: " . $config['api_token'] . "\n";
    echo "Cloud ID: " . $config['cloud_id'] . "\n";
    echo "Base URL: " . $config['base_url'] . "\n\n";
    
    // Summary
    echo "=== Test Summary ===\n";
    echo "Raw cURL HTTP Code: $httpCode\n";
    echo "Raw cURL Success: " . ($httpCode >= 200 && $httpCode < 300 ? 'YES' : 'NO') . "\n";
    echo "JSON Parsing: " . ($parsedResult !== null ? 'SUCCESS' : 'FAILED') . "\n";
    echo "Client Consistency: " . ($result === $clientResult ? 'CONSISTENT' : 'INCONSISTENT') . "\n";
    echo "Helper Consistency: " . ($result === $helperResult ? 'CONSISTENT' : 'INCONSISTENT') . "\n";
    
    if ($httpCode >= 200 && $httpCode < 300 && $parsedResult !== null) {
        echo "\n✅ SUCCESS: Exact cURL implementation is working correctly!\n";
    } else {
        echo "\n❌ ISSUES DETECTED: Check configuration and network connectivity\n";
    }
    
    echo "\n=== Web Test URLs ===\n";
    echo "Raw cURL Test: http://localhost:8080/curl/fingerspot/raw-test\n";
    echo "Complete cURL Suite: http://localhost:8080/curl/fingerspot/\n";
    echo "Get Attlog: http://localhost:8080/curl/fingerspot/get-attlog\n";
    echo "Configuration: http://localhost:8080/curl/fingerspot/config\n";
    echo "Interactive Test: http://localhost:8080/fingerspot_test.html\n";
    
    echo "\n=== cURL Code Used ===\n";
    echo "This test used the exact cURL code you provided:\n";
    echo "\$url = 'https://developer.fingerspot.io/api/get_attlog';\n";
    echo "\$data = '{\"trans_id\":\"1\", \"cloud_id\":\"[from_env]\", \"start_date\":\"[date]\", \"end_date\":\"[date]\"}';\n";
    echo "\$authorization = \"Authorization: Bearer [api_token_from_env]\";\n";
    echo "// ... exact cURL options as specified\n";

} catch (Exception $e) {
    echo "CRITICAL ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n\n";
    
    echo "Possible Issues:\n";
    echo "1. Environment variables not set in .env file\n";
    echo "2. Network connectivity problems\n";
    echo "3. Invalid API credentials\n";
    echo "4. CodeIgniter framework issues\n";
    echo "5. cURL extension not enabled\n";
}

echo "\n=== cURL Implementation Test Complete ===\n";
