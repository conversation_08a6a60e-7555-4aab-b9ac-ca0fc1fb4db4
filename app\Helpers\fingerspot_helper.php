<?php

/**
 * Fingerspot Helper Functions
 * 
 * Helper functions to make Fingerspot API usage even easier
 */

if (!function_exists('fingerspot_device')) {
    /**
     * Get device information
     * 
     * @return array
     */
    function fingerspot_device(): array
    {
        return \App\Libraries\FingerspotSdk::device();
    }
}

if (!function_exists('fingerspot_scanlog')) {
    /**
     * Get attendance logs
     * 
     * @param array $params Optional parameters
     * @return array
     */
    function fingerspot_scanlog(array $params = []): array
    {
        return \App\Libraries\FingerspotSdk::scanlog($params);
    }
}

if (!function_exists('fingerspot_scanlog_new')) {
    /**
     * Get new attendance logs
     * 
     * @param string|null $lastScanId Last scan ID
     * @return array
     */
    function fingerspot_scanlog_new(?string $lastScanId = null): array
    {
        return \App\Libraries\FingerspotSdk::scanlogNew($lastScanId);
    }
}

if (!function_exists('fingerspot_user_info')) {
    /**
     * Get user information
     * 
     * @param string|null $userPin User PIN, null for all users
     * @return array
     */
    function fingerspot_user_info(?string $userPin = null): array
    {
        return \App\Libraries\FingerspotSdk::userInfo($userPin);
    }
}

if (!function_exists('fingerspot_set_user')) {
    /**
     * Set/Add user to device
     * 
     * @param array $userData User data
     * @return array
     */
    function fingerspot_set_user(array $userData): array
    {
        return \App\Libraries\FingerspotSdk::setUser($userData);
    }
}

if (!function_exists('fingerspot_delete_user')) {
    /**
     * Delete user from device
     * 
     * @param string $userPin User PIN
     * @return array
     */
    function fingerspot_delete_user(string $userPin): array
    {
        return \App\Libraries\FingerspotSdk::deleteUser($userPin);
    }
}

if (!function_exists('fingerspot_restart_device')) {
    /**
     * Restart device
     * 
     * @return array
     */
    function fingerspot_restart_device(): array
    {
        return \App\Libraries\FingerspotSdk::restartDevice();
    }
}

if (!function_exists('fingerspot_set_time')) {
    /**
     * Set device time
     * 
     * @param string|null $datetime Datetime string
     * @param string|null $timezone Timezone string
     * @return array
     */
    function fingerspot_set_time(?string $datetime = null, ?string $timezone = null): array
    {
        return \App\Libraries\FingerspotSdk::setTime($datetime, $timezone);
    }
}

if (!function_exists('fingerspot_response')) {
    /**
     * Create FingerspotResponse object from array
     * 
     * @param array $response Response array
     * @return \App\Models\FingerspotResponse
     */
    function fingerspot_response(array $response): \App\Models\FingerspotResponse
    {
        return new \App\Models\FingerspotResponse($response);
    }
}

if (!function_exists('fingerspot_is_success')) {
    /**
     * Check if response is successful
     * 
     * @param array $response Response array
     * @return bool
     */
    function fingerspot_is_success(array $response): bool
    {
        return $response['success'] ?? false;
    }
}

if (!function_exists('fingerspot_get_data')) {
    /**
     * Get data from response
     * 
     * @param array $response Response array
     * @param mixed $default Default value if no data
     * @return mixed
     */
    function fingerspot_get_data(array $response, mixed $default = null): mixed
    {
        return $response['data'] ?? $default;
    }
}

if (!function_exists('fingerspot_get_message')) {
    /**
     * Get message from response
     * 
     * @param array $response Response array
     * @return string
     */
    function fingerspot_get_message(array $response): string
    {
        return $response['message'] ?? '';
    }
}
