<?php

namespace App\Libraries\Fingerspot;

use App\Libraries\FingerspotApiClient;

/**
 * Fingerspot Attendance Service
 * 
 * Service for handling attendance log operations
 */
class AttendanceService
{
    protected FingerspotApiClient $client;

    public function __construct()
    {
        $this->client = new FingerspotApiClient();
    }

    /**
     * Get attendance logs from device
     * 
     * @param array $params Optional parameters for filtering
     * @return array
     */
    public function getLogs(array $params = []): array
    {
        $endpoint = $this->client->getEndpoint('attendance_logs');
        return $this->client->get($endpoint, $params);
    }

    /**
     * Get new attendance logs (scanlog new)
     * 
     * @param string|null $lastScanId Last scan ID to get newer records
     * @return array
     */
    public function getNewLogs(?string $lastScanId = null): array
    {
        $params = [];
        if ($lastScanId) {
            $params['last_scan_id'] = $lastScanId;
        }

        $endpoint = $this->client->getEndpoint('attendance_logs') . '/new';
        return $this->client->get($endpoint, $params);
    }

    /**
     * Get attendance logs by date range
     * 
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @param array $additionalParams Additional parameters
     * @return array
     */
    public function getLogsByDateRange(string $startDate, string $endDate, array $additionalParams = []): array
    {
        $params = array_merge([
            'start_date' => $startDate,
            'end_date' => $endDate
        ], $additionalParams);

        $endpoint = $this->client->getEndpoint('attendance_logs');
        return $this->client->get($endpoint, $params);
    }

    /**
     * Get attendance logs by user PIN
     * 
     * @param string $userPin User PIN
     * @param array $additionalParams Additional parameters
     * @return array
     */
    public function getLogsByUser(string $userPin, array $additionalParams = []): array
    {
        $params = array_merge([
            'user_pin' => $userPin
        ], $additionalParams);

        $endpoint = $this->client->getEndpoint('attendance_logs');
        return $this->client->get($endpoint, $params);
    }

    /**
     * Get real-time attendance data
     * 
     * @return array
     */
    public function getRealTimeData(): array
    {
        $endpoint = $this->client->getEndpoint('attendance_logs') . '/realtime';
        return $this->client->get($endpoint);
    }

    /**
     * Clear attendance logs from device
     * 
     * @return array
     */
    public function clearLogs(): array
    {
        $endpoint = $this->client->getEndpoint('attendance_logs') . '/clear';
        return $this->client->post($endpoint);
    }

    /**
     * Get attendance statistics
     * 
     * @param array $params Parameters for statistics
     * @return array
     */
    public function getStatistics(array $params = []): array
    {
        $endpoint = $this->client->getEndpoint('attendance_logs') . '/statistics';
        return $this->client->get($endpoint, $params);
    }
}
