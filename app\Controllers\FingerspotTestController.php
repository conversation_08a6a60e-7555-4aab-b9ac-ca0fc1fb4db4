<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\EasylinkSdk;
use App\Libraries\FingerspotSdk;
use App\Models\FingerspotResponse;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Fingerspot Test Controller
 * 
 * Simple test controller to verify the API implementation
 */
class FingerspotTestController extends BaseController
{
    /**
     * Test basic SDK functionality using exact PDF documentation examples
     */
    public function index(): ResponseInterface
    {
        $tests = [];

        // Test 1: Device Info (as per PDF documentation)
        try {
            $deviceInfo = EasylinkSdk::device();
            $tests['pdf_example_device_info'] = [
                'test_name' => 'Device Info (PDF Example)',
                'endpoint' => 'device()',
                'status' => 'tested',
                'success' => $deviceInfo['success'] ?? false,
                'message' => $deviceInfo['message'] ?? 'No message',
                'has_data' => !empty($deviceInfo['data']),
                'response_sample' => $deviceInfo
            ];
        } catch (\Exception $e) {
            $tests['pdf_example_device_info'] = [
                'test_name' => 'Device Info (PDF Example)',
                'endpoint' => 'device()',
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Test 2: Get Attlog / Scanlog New (as per PDF documentation)
        try {
            $attendanceLogs = EasylinkSdk::scanlogNew();
            $tests['pdf_example_get_attlog'] = [
                'test_name' => 'Get Attlog - Scanlog New (PDF Example)',
                'endpoint' => 'scanlogNew()',
                'status' => 'tested',
                'success' => $attendanceLogs['success'] ?? false,
                'message' => $attendanceLogs['message'] ?? 'No message',
                'has_data' => !empty($attendanceLogs['data']),
                'response_sample' => $attendanceLogs
            ];
        } catch (\Exception $e) {
            $tests['pdf_example_get_attlog'] = [
                'test_name' => 'Get Attlog - Scanlog New (PDF Example)',
                'endpoint' => 'scanlogNew()',
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Test 3: Get Userinfo (as per PDF documentation)
        try {
            $userInfo = FingerspotSdk::userInfo();
            $response = new FingerspotResponse($userInfo);
            $tests['pdf_example_get_userinfo'] = [
                'test_name' => 'Get Userinfo (PDF Example)',
                'endpoint' => 'userInfo()',
                'status' => 'tested',
                'success' => $response->isSuccess(),
                'message' => $response->getMessage(),
                'status_code' => $response->getStatusCode(),
                'has_data' => $response->getData() !== null,
                'response_sample' => $userInfo
            ];
        } catch (\Exception $e) {
            $tests['pdf_example_get_userinfo'] = [
                'test_name' => 'Get Userinfo (PDF Example)',
                'endpoint' => 'userInfo()',
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Test 4: Set Userinfo (as per PDF documentation)
        try {
            // Example user data as per PDF documentation
            $userData = [
                'pin' => '999',
                'name' => 'Test User PDF',
                'privilege' => '0',
                'password' => '',
                'group_id' => '1',
                'user_id' => '999',
                'card_number' => '0'
            ];

            $setUserResult = FingerspotSdk::setUser($userData);
            $response = new FingerspotResponse($setUserResult);
            $tests['pdf_example_set_userinfo'] = [
                'test_name' => 'Set Userinfo (PDF Example)',
                'endpoint' => 'setUser()',
                'status' => 'tested',
                'success' => $response->isSuccess(),
                'message' => $response->getMessage(),
                'status_code' => $response->getStatusCode(),
                'user_data_sent' => $userData,
                'response_sample' => $setUserResult
            ];
        } catch (\Exception $e) {
            $tests['pdf_example_set_userinfo'] = [
                'test_name' => 'Set Userinfo (PDF Example)',
                'endpoint' => 'setUser()',
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Test 5: Set Time (as per PDF documentation)
        try {
            $setTimeResult = FingerspotSdk::setTime();
            $response = new FingerspotResponse($setTimeResult);
            $tests['pdf_example_set_time'] = [
                'test_name' => 'Set Time (PDF Example)',
                'endpoint' => 'setTime()',
                'status' => 'tested',
                'success' => $response->isSuccess(),
                'message' => $response->getMessage(),
                'status_code' => $response->getStatusCode(),
                'response_sample' => $setTimeResult
            ];
        } catch (\Exception $e) {
            $tests['pdf_example_set_time'] = [
                'test_name' => 'Set Time (PDF Example)',
                'endpoint' => 'setTime()',
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Test 6: Restart Device (as per PDF documentation)
        try {
            $restartResult = FingerspotSdk::restartDevice();
            $response = new FingerspotResponse($restartResult);
            $tests['pdf_example_restart_device'] = [
                'test_name' => 'Restart Device (PDF Example)',
                'endpoint' => 'restartDevice()',
                'status' => 'tested',
                'success' => $response->isSuccess(),
                'message' => $response->getMessage(),
                'status_code' => $response->getStatusCode(),
                'response_sample' => $restartResult
            ];
        } catch (\Exception $e) {
            $tests['pdf_example_restart_device'] = [
                'test_name' => 'Restart Device (PDF Example)',
                'endpoint' => 'restartDevice()',
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Test 7: Register Online (as per PDF documentation)
        try {
            $registerData = [
                'pin' => '999',
                'template_type' => 'fingerprint',
                'template_data' => 'sample_template_data_for_testing',
                'finger_index' => 0
            ];

            $registerResult = FingerspotSdk::registerOnline($registerData);
            $response = new FingerspotResponse($registerResult);
            $tests['pdf_example_register_online'] = [
                'test_name' => 'Register Online (PDF Example)',
                'endpoint' => 'registerOnline()',
                'status' => 'tested',
                'success' => $response->isSuccess(),
                'message' => $response->getMessage(),
                'status_code' => $response->getStatusCode(),
                'register_data_sent' => $registerData,
                'response_sample' => $registerResult
            ];
        } catch (\Exception $e) {
            $tests['pdf_example_register_online'] = [
                'test_name' => 'Register Online (PDF Example)',
                'endpoint' => 'registerOnline()',
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Summary
        $totalTests = count($tests);
        $successfulTests = count(array_filter($tests, function($test) {
            return $test['status'] === 'tested' && ($test['success'] ?? false);
        }));
        $errorTests = count(array_filter($tests, function($test) {
            return $test['status'] === 'error';
        }));

        return $this->response->setJSON([
            'fingerspot_api_test_results' => [
                'summary' => [
                    'total_tests' => $totalTests,
                    'successful_tests' => $successfulTests,
                    'error_tests' => $errorTests,
                    'success_rate' => $totalTests > 0 ? round(($successfulTests / $totalTests) * 100, 2) . '%' : '0%'
                ],
                'tests' => $tests,
                'configuration' => [
                    'base_url' => config('Fingerspot')->baseUrl,
                    'api_token' => substr(config('Fingerspot')->apiToken, 0, 8) . '...',
                    'cloud_id' => config('Fingerspot')->cloudId,
                    'debug_mode' => config('Fingerspot')->debug
                ],
                'recommendations' => [
                    'If all tests show errors, check your API credentials and network connection',
                    'If some tests succeed, the API client is working correctly',
                    'Enable debug mode in Config/Fingerspot.php to see detailed API logs',
                    'Check the Fingerspot device is online and accessible'
                ]
            ]
        ]);
    }

    /**
     * Test specific functionality
     */
    public function testSpecific(string $test = 'device'): ResponseInterface
    {
        switch ($test) {
            case 'device':
                return $this->testDevice();
            case 'attendance':
                return $this->testAttendance();
            case 'user':
                return $this->testUser();
            default:
                return $this->response->setJSON([
                    'error' => 'Invalid test type. Available: device, attendance, user'
                ])->setStatusCode(400);
        }
    }

    private function testDevice(): ResponseInterface
    {
        $deviceService = FingerspotSdk::deviceService();
        
        $tests = [
            'device_info' => $deviceService->getDeviceInfo(),
            'test_connection' => $deviceService->testConnection(),
            'get_status' => $deviceService->getStatus(),
            'get_capacity' => $deviceService->getCapacity()
        ];

        return $this->response->setJSON(['device_tests' => $tests]);
    }

    private function testAttendance(): ResponseInterface
    {
        $attendanceService = FingerspotSdk::attendance();
        
        $tests = [
            'get_logs' => $attendanceService->getLogs(),
            'get_new_logs' => $attendanceService->getNewLogs(),
            'get_realtime' => $attendanceService->getRealTimeData()
        ];

        return $this->response->setJSON(['attendance_tests' => $tests]);
    }

    private function testUser(): ResponseInterface
    {
        $userService = FingerspotSdk::user();
        
        $tests = [
            'get_all_users' => $userService->getAllUsers(),
            'get_user_count' => $userService->getUserCount()
        ];

        return $this->response->setJSON(['user_tests' => $tests]);
    }
}
