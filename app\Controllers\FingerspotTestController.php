<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Libraries\EasylinkSdk;
use App\Libraries\FingerspotSdk;
use App\Models\FingerspotResponse;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Fingerspot Test Controller
 * 
 * Simple test controller to verify the API implementation
 */
class FingerspotTestController extends BaseController
{
    /**
     * Test basic SDK functionality
     */
    public function index(): ResponseInterface
    {
        $tests = [];
        
        // Test 1: Basic EasylinkSdk usage (as shown in credentials file)
        try {
            $deviceInfo = EasylinkSdk::device();
            $tests['easylink_device'] = [
                'status' => 'tested',
                'success' => $deviceInfo['success'] ?? false,
                'message' => $deviceInfo['message'] ?? 'No message',
                'has_data' => !empty($deviceInfo['data'])
            ];
        } catch (\Exception $e) {
            $tests['easylink_device'] = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Test 2: EasylinkSdk scanlogNew
        try {
            $attendanceLogs = EasylinkSdk::scanlogNew();
            $tests['easylink_scanlog'] = [
                'status' => 'tested',
                'success' => $attendanceLogs['success'] ?? false,
                'message' => $attendanceLogs['message'] ?? 'No message',
                'has_data' => !empty($attendanceLogs['data'])
            ];
        } catch (\Exception $e) {
            $tests['easylink_scanlog'] = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Test 3: FingerspotSdk device info
        try {
            $result = FingerspotSdk::device();
            $response = new FingerspotResponse($result);
            $tests['fingerspot_device'] = [
                'status' => 'tested',
                'success' => $response->isSuccess(),
                'message' => $response->getMessage(),
                'status_code' => $response->getStatusCode(),
                'has_data' => $response->getData() !== null
            ];
        } catch (\Exception $e) {
            $tests['fingerspot_device'] = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Test 4: User service
        try {
            $userService = FingerspotSdk::user();
            $result = $userService->getAllUsers();
            $response = new FingerspotResponse($result);
            $tests['user_service'] = [
                'status' => 'tested',
                'success' => $response->isSuccess(),
                'message' => $response->getMessage(),
                'status_code' => $response->getStatusCode()
            ];
        } catch (\Exception $e) {
            $tests['user_service'] = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Test 5: Attendance service
        try {
            $attendanceService = FingerspotSdk::attendance();
            $result = $attendanceService->getLogs();
            $response = new FingerspotResponse($result);
            $tests['attendance_service'] = [
                'status' => 'tested',
                'success' => $response->isSuccess(),
                'message' => $response->getMessage(),
                'status_code' => $response->getStatusCode()
            ];
        } catch (\Exception $e) {
            $tests['attendance_service'] = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Test 6: Device service
        try {
            $deviceService = FingerspotSdk::deviceService();
            $result = $deviceService->testConnection();
            $response = new FingerspotResponse($result);
            $tests['device_service'] = [
                'status' => 'tested',
                'success' => $response->isSuccess(),
                'message' => $response->getMessage(),
                'status_code' => $response->getStatusCode()
            ];
        } catch (\Exception $e) {
            $tests['device_service'] = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }

        // Summary
        $totalTests = count($tests);
        $successfulTests = count(array_filter($tests, function($test) {
            return $test['status'] === 'tested' && ($test['success'] ?? false);
        }));
        $errorTests = count(array_filter($tests, function($test) {
            return $test['status'] === 'error';
        }));

        return $this->response->setJSON([
            'fingerspot_api_test_results' => [
                'summary' => [
                    'total_tests' => $totalTests,
                    'successful_tests' => $successfulTests,
                    'error_tests' => $errorTests,
                    'success_rate' => $totalTests > 0 ? round(($successfulTests / $totalTests) * 100, 2) . '%' : '0%'
                ],
                'tests' => $tests,
                'configuration' => [
                    'base_url' => config('Fingerspot')->baseUrl,
                    'api_token' => substr(config('Fingerspot')->apiToken, 0, 8) . '...',
                    'cloud_id' => config('Fingerspot')->cloudId,
                    'debug_mode' => config('Fingerspot')->debug
                ],
                'recommendations' => [
                    'If all tests show errors, check your API credentials and network connection',
                    'If some tests succeed, the API client is working correctly',
                    'Enable debug mode in Config/Fingerspot.php to see detailed API logs',
                    'Check the Fingerspot device is online and accessible'
                ]
            ]
        ]);
    }

    /**
     * Test specific functionality
     */
    public function testSpecific(string $test = 'device'): ResponseInterface
    {
        switch ($test) {
            case 'device':
                return $this->testDevice();
            case 'attendance':
                return $this->testAttendance();
            case 'user':
                return $this->testUser();
            default:
                return $this->response->setJSON([
                    'error' => 'Invalid test type. Available: device, attendance, user'
                ])->setStatusCode(400);
        }
    }

    private function testDevice(): ResponseInterface
    {
        $deviceService = FingerspotSdk::deviceService();
        
        $tests = [
            'device_info' => $deviceService->getDeviceInfo(),
            'test_connection' => $deviceService->testConnection(),
            'get_status' => $deviceService->getStatus(),
            'get_capacity' => $deviceService->getCapacity()
        ];

        return $this->response->setJSON(['device_tests' => $tests]);
    }

    private function testAttendance(): ResponseInterface
    {
        $attendanceService = FingerspotSdk::attendance();
        
        $tests = [
            'get_logs' => $attendanceService->getLogs(),
            'get_new_logs' => $attendanceService->getNewLogs(),
            'get_realtime' => $attendanceService->getRealTimeData()
        ];

        return $this->response->setJSON(['attendance_tests' => $tests]);
    }

    private function testUser(): ResponseInterface
    {
        $userService = FingerspotSdk::user();
        
        $tests = [
            'get_all_users' => $userService->getAllUsers(),
            'get_user_count' => $userService->getUserCount()
        ];

        return $this->response->setJSON(['user_tests' => $tests]);
    }
}
